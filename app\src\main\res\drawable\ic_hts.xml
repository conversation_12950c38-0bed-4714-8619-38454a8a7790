<!--
  ~ Copyright (c) 2022, Nordic Semiconductor
  ~ All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without modification, are
  ~ permitted provided that the following conditions are met:
  ~
  ~ 1. Redistributions of source code must retain the above copyright notice, this list of
  ~ conditions and the following disclaimer.
  ~
  ~ 2. Redistributions in binary form must reproduce the above copyright notice, this list
  ~ of conditions and the following disclaimer in the documentation and/or other materials
  ~ provided with the distribution.
  ~
  ~ 3. Neither the name of the copyright holder nor the names of its contributors may be
  ~ used to endorse or promote products derived from this software without specific prior
  ~ written permission.
  ~
  ~ THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  ~ "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
  ~ TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
  ~ PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
  ~ HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  ~ <PERSON>ECIA<PERSON>, E<PERSON>EMPLAR<PERSON>, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
  ~ LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
  ~ OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  ~ OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  ~ NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
  ~ EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="80dp"
    android:height="80dp"
    android:viewportWidth="1024"
    android:viewportHeight="1024">
    <path
        android:fillColor="#00B3DC"
        android:pathData="M813.8,338h-92.9c-15.7,0 -28.5,12.8 -28.5,28.5s12.8,28.5 28.5,28.5h92.9c15.7,0 28.5,-12.8 28.5,-28.5S829.6,338 813.8,338z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M720.9,240.7h92.9c15.7,0 28.5,-12.8 28.5,-28.5s-12.8,-28.5 -28.5,-28.5h-92.9c-15.7,0 -28.5,12.8 -28.5,28.5S705.2,240.7 720.9,240.7z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M813.8,492.3h-92.9c-15.7,0 -28.5,12.8 -28.5,28.5c0,15.7 12.8,28.5 28.5,28.5h92.9c15.7,0 28.5,-12.8 28.5,-28.5C842.3,505.1 829.6,492.3 813.8,492.3z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M637.5,604.9V175.3c0,-66.8 -54.3,-121.1 -121.1,-121.1s-121.1,54.3 -121.1,121.1v429.6c-18.2,15.5 -33.5,34.6 -44.6,55.8c-13.9,26.5 -21.2,56.4 -21.2,86.5c0,103 83.8,186.8 186.8,186.8c103,0 186.8,-83.8 186.8,-186.8c0,-30.1 -7.3,-60 -21.2,-86.5C671,639.5 655.7,620.5 637.5,604.9zM516.4,877c-71.6,0 -129.8,-58.2 -129.8,-129.8c0,-41.6 20.2,-80.9 53.9,-105.3c7.4,-5.4 11.8,-14 11.8,-23.1V175.3c0,-35.3 28.7,-64.1 64.1,-64.1c35.3,0 64.1,28.7 64.1,64.1v443.4c0,9.2 4.4,17.8 11.8,23.1c33.8,24.4 53.9,63.8 53.9,105.3C646.2,818.8 588,877 516.4,877z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M601.3,747c0,-1.3 0,-2.7 -0.1,-4c0,-0.4 -0.1,-0.8 -0.1,-1.2c-0.1,-0.9 -0.1,-1.8 -0.2,-2.8c0,-0.5 -0.1,-0.9 -0.1,-1.4c-0.1,-0.9 -0.2,-1.8 -0.3,-2.7c-0.1,-0.4 -0.1,-0.8 -0.2,-1.2c-0.2,-1.3 -0.4,-2.6 -0.7,-3.8c0,0 0,-0.1 0,-0.1c-0.3,-1.2 -0.5,-2.4 -0.8,-3.6c-0.1,-0.4 -0.2,-0.8 -0.3,-1.2c-0.2,-0.9 -0.5,-1.7 -0.7,-2.5c-0.1,-0.4 -0.3,-0.9 -0.4,-1.3c-0.3,-0.8 -0.5,-1.7 -0.8,-2.5c-0.1,-0.4 -0.3,-0.8 -0.4,-1.1c-0.4,-1.2 -0.9,-2.3 -1.4,-3.5c0,-0.1 -0.1,-0.2 -0.1,-0.2c-0.5,-1.1 -0.9,-2.1 -1.4,-3.2c-0.2,-0.4 -0.4,-0.8 -0.6,-1.1c-0.4,-0.8 -0.8,-1.5 -1.2,-2.2c-0.2,-0.4 -0.4,-0.8 -0.7,-1.2c-0.4,-0.7 -0.8,-1.5 -1.3,-2.2c-0.2,-0.4 -0.4,-0.7 -0.6,-1.1c-0.6,-1 -1.3,-2 -1.9,-3c-0.1,-0.2 -0.2,-0.3 -0.3,-0.5c-0.6,-0.9 -1.2,-1.8 -1.9,-2.6c-0.3,-0.4 -0.5,-0.7 -0.8,-1.1c-0.5,-0.6 -1,-1.3 -1.5,-1.9c-0.3,-0.4 -0.6,-0.8 -0.9,-1.1c-0.5,-0.6 -1,-1.2 -1.6,-1.8c-0.3,-0.3 -0.6,-0.7 -0.9,-1c-0.8,-0.8 -1.5,-1.7 -2.3,-2.5c-0.2,-0.2 -0.4,-0.4 -0.6,-0.6c-0.7,-0.7 -1.4,-1.4 -2.2,-2.1c-0.4,-0.3 -0.7,-0.6 -1.1,-0.9c-0.6,-0.5 -1.2,-1 -1.7,-1.5c-0.4,-0.3 -0.8,-0.7 -1.2,-1c-0.6,-0.5 -1.2,-1 -1.8,-1.4c-0.4,-0.3 -0.7,-0.6 -1.1,-0.9c-0.9,-0.6 -1.8,-1.3 -2.7,-1.9c-0.3,-0.2 -0.6,-0.4 -1,-0.6c-0.8,-0.5 -1.6,-1 -2.4,-1.5c-0.4,-0.3 -0.9,-0.5 -1.3,-0.8c-0.6,-0.4 -1.3,-0.8 -2,-1.1c-0.5,-0.3 -0.9,-0.5 -1.4,-0.8c-0.7,-0.4 -1.4,-0.7 -2,-1c-0.4,-0.2 -0.9,-0.4 -1.3,-0.7c-1,-0.5 -2,-0.9 -3,-1.4c-0.4,-0.2 -0.8,-0.3 -1.1,-0.5c-0.9,-0.4 -1.7,-0.7 -2.6,-1c-0.5,-0.2 -1,-0.3 -1.4,-0.5c-0.4,-0.2 -0.9,-0.3 -1.3,-0.5V334.1l-53.8,0.8v331.6c-0.4,0.1 -0.9,0.3 -1.3,0.5c-0.5,0.2 -1,0.3 -1.4,0.5c-0.9,0.3 -1.8,0.7 -2.6,1c-0.4,0.2 -0.8,0.3 -1.1,0.5c-1,0.4 -2,0.9 -3,1.4c-0.4,0.2 -0.9,0.4 -1.3,0.7c-0.7,0.3 -1.4,0.7 -2,1c-0.5,0.2 -0.9,0.5 -1.4,0.8c-0.7,0.4 -1.3,0.7 -2,1.1c-0.4,0.3 -0.9,0.5 -1.3,0.8c-0.8,0.5 -1.6,1 -2.4,1.5c-0.3,0.2 -0.6,0.4 -1,0.6c-0.9,0.6 -1.8,1.3 -2.7,1.9c-0.4,0.3 -0.8,0.6 -1.1,0.9c-0.6,0.5 -1.2,0.9 -1.8,1.4c-0.4,0.3 -0.8,0.6 -1.2,1c-0.6,0.5 -1.2,1 -1.7,1.5c-0.4,0.3 -0.7,0.6 -1.1,0.9c-0.7,0.7 -1.5,1.4 -2.2,2.1c-0.2,0.2 -0.4,0.4 -0.6,0.6c-0.8,0.8 -1.6,1.6 -2.3,2.5c-0.3,0.3 -0.6,0.7 -0.9,1c-0.5,0.6 -1.1,1.2 -1.6,1.8c-0.3,0.4 -0.6,0.7 -0.9,1.1c-0.5,0.6 -1,1.3 -1.5,1.9c-0.3,0.4 -0.5,0.7 -0.8,1.1c-0.6,0.9 -1.3,1.7 -1.9,2.6c-0.1,0.2 -0.2,0.3 -0.3,0.5c-0.7,1 -1.3,2 -1.9,3c-0.2,0.4 -0.4,0.7 -0.6,1.1c-0.4,0.7 -0.8,1.4 -1.3,2.2c-0.2,0.4 -0.4,0.8 -0.7,1.2c-0.4,0.7 -0.8,1.5 -1.2,2.2c-0.2,0.4 -0.4,0.8 -0.6,1.1c-0.5,1.1 -1,2.1 -1.4,3.2c0,0.1 -0.1,0.2 -0.1,0.2c-0.5,1.2 -0.9,2.3 -1.4,3.5c-0.1,0.4 -0.3,0.8 -0.4,1.1c-0.3,0.8 -0.6,1.7 -0.8,2.5c-0.1,0.4 -0.3,0.9 -0.4,1.3c-0.3,0.8 -0.5,1.7 -0.7,2.5c-0.1,0.4 -0.2,0.8 -0.3,1.2c-0.3,1.2 -0.6,2.4 -0.8,3.6c0,0 0,0.1 0,0.1c-0.3,1.3 -0.5,2.5 -0.7,3.8c-0.1,0.4 -0.1,0.8 -0.2,1.2c-0.1,0.9 -0.2,1.8 -0.3,2.7c0,0.5 -0.1,0.9 -0.1,1.4c-0.1,0.9 -0.2,1.8 -0.2,2.8c0,0.4 -0.1,0.8 -0.1,1.2c-0.1,1.3 -0.1,2.7 -0.1,4c0,0 0,0 0,0v0c0,0 0,0 0,0c0,20.5 7.3,39.3 19.4,54c15.6,18.9 39.1,30.9 65.5,30.9c26.4,0 49.9,-12 65.5,-30.9C594,786.3 601.3,767.5 601.3,747C601.3,747 601.3,747 601.3,747L601.3,747C601.3,747 601.3,747 601.3,747z" />
</vector>
