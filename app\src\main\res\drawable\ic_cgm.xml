<!--
  ~ Copyright (c) 2022, Nordic Semiconductor
  ~ All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without modification, are
  ~ permitted provided that the following conditions are met:
  ~
  ~ 1. Redistributions of source code must retain the above copyright notice, this list of
  ~ conditions and the following disclaimer.
  ~
  ~ 2. Redistributions in binary form must reproduce the above copyright notice, this list
  ~ of conditions and the following disclaimer in the documentation and/or other materials
  ~ provided with the distribution.
  ~
  ~ 3. Neither the name of the copyright holder nor the names of its contributors may be
  ~ used to endorse or promote products derived from this software without specific prior
  ~ written permission.
  ~
  ~ THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  ~ "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
  ~ TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
  ~ PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
  ~ HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  ~ <PERSON>ECIA<PERSON>, E<PERSON>EMPLAR<PERSON>, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
  ~ LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
  ~ OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  ~ OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  ~ NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
  ~ EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="80dp"
    android:height="80dp"
    android:viewportWidth="1024"
    android:viewportHeight="1024">
    <path
        android:fillColor="#00B3DC"
        android:pathData="M236.3,441.9c-77.1,0 -139.8,-61 -139.8,-135.9c0,-20.6 4.6,-40.4 13.8,-58.8c0,0 0,-0.1 0.1,-0.1c3.6,-7.2 7.8,-14.1 12.6,-20.6L212.1,83c5.8,-9.4 16.5,-14.6 27.5,-13.3c9.6,1.1 17.8,7 22.1,15.4c4.3,7.4 39.3,63.6 90,144.2c2.8,4 5.4,8.2 7.8,12.5c0.2,0.4 0.4,0.8 0.6,1.1c10.5,19.4 16,41.2 16,63.1C376.1,380.9 313.4,441.9 236.3,441.9zM161.3,272.6c-5.2,10.5 -7.8,21.7 -7.8,33.4c0,43.5 37.1,78.9 82.8,78.9s82.8,-35.4 82.8,-78.9c0,-12.8 -3.1,-25 -9.3,-36.3c0,0 0,0 0,0c0,0 0,0 0,-0.1c-1.5,-2.7 -3.2,-5.4 -5,-7.9c-0.3,-0.5 -0.6,-0.9 -0.9,-1.4c-11.6,-18.4 -34,-54.1 -53.9,-85.9c-5.3,-8.4 -9.8,-15.7 -13.8,-22.1l-65.4,105.3c-0.5,0.8 -1,1.5 -1.5,2.2c-3,4 -5.6,8.2 -7.8,12.5C161.4,272.4 161.4,272.5 161.3,272.6zM263.3,89C263.3,89 263.3,89 263.3,89C263.3,89 263.3,89 263.3,89zM263.3,88.9C263.3,88.9 263.3,89 263.3,88.9C263.3,89 263.3,88.9 263.3,88.9z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M403.9,1011.3c-22.3,0 -43.8,-7.6 -61.4,-21.9c0,0 0,0 0,0L167.7,847c-41.7,-33.9 -47.9,-95.5 -14,-137.1L679.2,64.7C695.7,44.5 719,32 744.9,29.3c25.9,-2.6 51.3,5 71.5,21.4l174.8,142.4c20.2,16.4 32.8,39.8 35.4,65.7s-5,51.3 -21.4,71.5L479.6,975.4c-16.4,20.2 -39.8,32.8 -65.7,35.4C410.6,1011.2 407.2,1011.3 403.9,1011.3zM378.5,945.2c8.4,6.8 18.9,10 29.7,8.9c10.7,-1.1 20.4,-6.3 27.3,-14.7L961,294.2c6.8,-8.4 10,-18.9 8.9,-29.7c-1.1,-10.7 -6.3,-20.4 -14.7,-27.3L780.3,94.9c-8.4,-6.8 -18.9,-10 -29.7,-8.9c-10.8,1.1 -20.4,6.3 -27.3,14.7L197.9,745.9c-14.1,17.3 -11.5,42.8 5.8,56.9L378.5,945.2L378.5,945.2z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M572,414.3m-41.9,0a41.9,41.9 0,1 1,83.8 0a41.9,41.9 0,1 1,-83.8 0" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M684.5,505.9m-41.9,0a41.9,41.9 0,1 1,83.8 0a41.9,41.9 0,1 1,-83.8 0" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M474.3,534.2m-41.9,0a41.9,41.9 0,1 1,83.8 0a41.9,41.9 0,1 1,-83.8 0" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M586.8,625.8m-41.9,0a41.9,41.9 0,1 1,83.8 0a41.9,41.9 0,1 1,-83.8 0" />
</vector>
