package no.nordicsemi.android.uart.view

import android.util.Log
import com.github.mikephil.charting.data.Entry
import java.util.concurrent.ConcurrentLinkedQueue

private const val POOL_SIZE = 15300 // 5100 * 3，支持三个图表

/**
 * Entry对象池
 * 用于复用Entry对象，减少对象创建和GC压力
 */
internal object EntryPool {
    private val pool = ConcurrentLinkedQueue<Entry>()
    private var created = 0

    init {
        // 预创建一定数量的Entry对象
        repeat(POOL_SIZE / 2) {
            pool.offer(Entry(0f, 0f))
            created++
        }
    }

    fun acquire(x: Float, y: Float): Entry {
        val entry = pool.poll() ?: createNewEntry()
        entry.x = x
        entry.y = y
        return entry
    }

    fun release(entry: Entry) {
        if (pool.size < POOL_SIZE) {
            pool.offer(entry)
        }
    }

    fun releaseAll(entries: List<Entry>) {
        entries.forEach { release(it) }
    }

    private fun createNewEntry(): Entry {
        created++
//        Log.d("EntryPool", "Created new Entry object. Total created: $created")
        return Entry(0f, 0f)
    }

    fun getStats(): String {
        return "Pool size: ${pool.size}, Total created: $created"
    }
}