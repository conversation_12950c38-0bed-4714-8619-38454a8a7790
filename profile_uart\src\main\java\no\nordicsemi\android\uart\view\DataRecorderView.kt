/**
 * 数据图表视图
 *
 * 实现了温湿度数据的图表显示界面：
 * 1. 主要组件：
 *    - 图表标题栏：包含缩放控制
 *    - 折线图：显示温湿度数据
 *    - 功能按钮区：
 *      * 保存数据到CSV
 *      * 断开连接
 *
 * 2. 交互功能：
 *    - 图表缩放控制
 *    - 数据导出功能
 *    - 设备断开连接
 *
 * 3. 布局特点：
 *    - 垂直布局结构
 *    - 合理的间距
 *    - 响应式设计
 *
 * 4. 数据处理：
 *    - 支持实时数据更新
 *    - CSV格式数据导出
 */

package no.nordicsemi.android.uart.view

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import no.nordicsemi.android.uart.data.UARTServiceData
import kotlinx.coroutines.*
import android.util.Log
import androidx.compose.ui.text.style.TextAlign
import no.nordicsemi.android.kotlin.ble.core.data.GattConnectionState
import no.nordicsemi.android.uart.data.DataStorageManager

@Composable
internal fun DataRecorderView(state: UARTServiceData, onEvent: (UARTViewEvent) -> Unit) {
    val scope = rememberCoroutineScope()

    // 记录状态
    var isRecording by remember { mutableStateOf(false) }
    var startNanoTime by remember { mutableStateOf(0L) }

    // 监听连接状态变化
    LaunchedEffect(state.connectionState) {
        when (state.connectionState?.state) {
            GattConnectionState.STATE_CONNECTED -> {
//                Log.d("DATA_RECORDER", "设备已连接")
            }
            GattConnectionState.STATE_DISCONNECTED -> {
//                Log.d("DATA_RECORDER", "设备已断开")
                isRecording = false
            }
            else -> {
//                Log.d("DATA_RECORDER", "连接状态: ${state.connectionState?.state}")
            }
        }
    }

    // 界面布局
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 记录状态显示
        Text(
            text = if (isRecording) "Recording..." else "Stopped",
            style = MaterialTheme.typography.titleMedium
        )

        Spacer(modifier = Modifier.height(16.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 开始记录按钮
            Button(
                modifier = Modifier.weight(1f),
                onClick = {
                    if (!isRecording) {
                        isRecording = true
                        startNanoTime = System.nanoTime()
                    }
                },
                enabled = !isRecording && state.connectionState?.state == GattConnectionState.STATE_CONNECTED
            ) {
//                Text("Start Recording")
                Text(
                    text = "Start Recording",
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // 停止记录按钮
            Button(
                modifier = Modifier.weight(1f),
                onClick = {
                    isRecording = false
                },
                enabled = isRecording
            ) {
//                Text("Stop Recording")
                Text(
                    text = "Stop Recording",
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 显示数据存储管理器
        if (state.connectionState?.state == GattConnectionState.STATE_CONNECTED) {
            DataStorageManager(
                bodydataPoints = state.recordBodyMessages,
                ambientdataPoints = state.recordAmbientMessages,
                isRecording = isRecording
            )
        }
    }
}

