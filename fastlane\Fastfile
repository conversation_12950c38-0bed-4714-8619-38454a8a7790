# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Submit a new Beta Build to Crashlytics Beta"
  lane :beta do
    gradle(task: "clean assembleRelease")
    gradle(task: "test")
    crashlytics
  
    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    gradle(task: "clean assembleRelease")
    gradle(task: "test")
    upload_to_play_store
  end

  desc "Deploy build to Alpha channel."
  lane :deployAlpha do
    gradle(task: "clean bundleRelease")
    gradle(task: "test")
    upload_to_play_store(
      track: 'alpha',
      aab: 'app/build/outputs/bundle/release/app-release.aab'
    )
  end

  desc "Deploy build to Beta channel."
    lane :deployBeta do
      gradle(task: "clean bundleRelease")
      gradle(task: "test")
      upload_to_play_store(
        track: 'beta',
        aab: 'app/build/outputs/bundle/release/app-release.aab'
      )
  end

  desc "Deploy build to internal channel."
    lane :deployInternal do
      gradle(task: "clean bundleRelease")
      upload_to_play_store(
        track: 'internal',
        aab: 'app/build/outputs/bundle/release/app-release.aab'
      )
  end

  desc "Generate internal Play Store link to the apk"
  lane :deployInternalLink do
    gradle(task: "clean bundleRelease")
    gradle(task: "test")
    upload_to_play_store_internal_app_sharing(aab: 'sample/build/outputs/bundle/release/sample-release.aab')
  end

end
