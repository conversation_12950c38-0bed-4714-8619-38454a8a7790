/**
 * 文件存储帮助类
 *
 * 提供体声和环境声音数据的CSV文件存储功能：
 * 1. 数据格式：
 *    - 时间戳（分:秒）
 *    - 体声数据
 *    - 环境声音数据
 *
 * 2. 存储特点：
 *    - 使用协程在后台线程执行文件操作
 *    - 自动生成带时间戳的文件名
 *    - 保存在应用的外部文档目录
 *    - CSV格式便于数据分析和导出
 *
 * 3. 用户反馈：
 *    - 保存成功或失败时显示Toast提示
 *    - 提示信息包含保存的文件名
 */

package no.nordicsemi.android.uart.data

import android.content.Context
import android.os.Environment
import android.widget.Toast
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStreamWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object FileStorageHelper {

    // 保存体声和环境声音数据到CSV文件
    fun saveDataToCSV(
        context: Context,
        bodyMessages: List<Int>,
        ambientMessages: List<Int>
    ) {
        // 启动协程，在IO线程中执行文件写入操作
        CoroutineScope(Dispatchers.IO).launch {
            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(System.currentTimeMillis())
            val fileName = "SoundData_$timeStamp.csv"
            val file = File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), fileName)

            val result = try {
                // 创建文件输出流
                val fileOutputStream = FileOutputStream(file)
                val writer = OutputStreamWriter(fileOutputStream)

                // 写入CSV文件头
                writer.append("Elapsed Time (min:sec),Body Sound,Ambient Sound\n")

                // 写入每一条声音数据记录，从时间 00:00 开始计数
                for (i in bodyMessages.indices) {
                    // 根据索引 i 计算分钟和秒数，假设每条记录时间间隔为 1 秒
                    val elapsedTimeInSeconds = i // 每次增加 1 秒
                    val minutes = elapsedTimeInSeconds / 60 // 计算分钟数
                    val seconds = elapsedTimeInSeconds % 60 // 计算秒数

                    // 格式化时间为 00:00 格式
                    val formattedTime = String.format("%02d:%02d", minutes, seconds)

                    // 获取体声和环境声音数据
                    val bodySound = bodyMessages.getOrElse(i) { 0 }
                    val ambientSound = ambientMessages.getOrElse(i) { 0 }

                    // 写入格式化后的数据 (Elapsed Time, bodySound, ambientSound)
                    writer.append("$formattedTime,$bodySound,$ambientSound\n")
                }

                writer.flush()
                writer.close()

                true // 文件保存成功
            } catch (e: Exception) {
                false // 文件保存失败
            }

            // 在主线程上显示Toast提示
            withContext(Dispatchers.Main) {
                if (result) {
                    Toast.makeText(context, "Data saved to $fileName", Toast.LENGTH_LONG).show()
                } else {
                    Toast.makeText(context, "Failed to save data", Toast.LENGTH_LONG).show()
                }
            }
        }
    }
}









///**
// * 文件存储帮助类
// *
// * 提供温湿度数据的CSV文件存储功能：
// * 1. 数据格式：
// *    - 时间戳（分:秒）
// *    - 温度数据
// *    - 湿度数据
// *
// * 2. 存储特点：
// *    - 使用协程在后台线程执行文件操作
// *    - 自动生成带时间戳的文件名
// *    - 保存在应用的外部文档目录
// *    - CSV格式便于数据分析和导出
// *
// * 3. 用户反馈：
// *    - 保存成功或失败时显示Toast提示
// *    - 提示信息包含保存的文件名
// */
//
//package no.nordicsemi.android.uart.data
//
//
//
//import android.content.Context
//import android.os.Environment
//import android.widget.Toast
//import java.io.File
//import java.io.FileOutputStream
//import java.io.OutputStreamWriter
//import java.text.SimpleDateFormat
//import java.util.Date
//import java.util.Locale
//
//import kotlinx.coroutines.CoroutineScope
//import kotlinx.coroutines.Dispatchers
//import kotlinx.coroutines.launch
//import kotlinx.coroutines.withContext
//
//
//object FileStorageHelper {
//
//    // 保存温湿度数据到CSV文件
//    fun saveDataToCSV(
//        context: Context,
//        temperatureMessages: List<Int>,
//        humidityMessages: List<Int>
//    ) {
//        // 启动协程，在IO线程中执行文件写入操作
//        CoroutineScope(Dispatchers.IO).launch {
//            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(System.currentTimeMillis())
//            val fileName = "TemperatureHumidity_$timeStamp.csv"
//            val file = File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), fileName)
//
//            val result = try {
//                // 创建文件输出流
//                val fileOutputStream = FileOutputStream(file)
//                val writer = OutputStreamWriter(fileOutputStream)
//
//                // 写入CSV文件头
//                writer.append("Elapsed Time (min:sec),Temperature,Humidity\n")
//
//                // 写入每一条温湿度记录，从时间 00:00 开始计数
//                for (i in temperatureMessages.indices) {
//                    // 根据索引 i 计算分钟和秒数，假设每条记录时间间隔为 1 秒
//                    val elapsedTimeInSeconds = i // 每次增加 1 秒
//                    val minutes = elapsedTimeInSeconds / 60 // 计算分钟数
//                    val seconds = elapsedTimeInSeconds % 60 // 计算秒数
//
//                    // 格式化时间为 00:00 格式
//                    val formattedTime = String.format("%02d:%02d", minutes, seconds)
//
//                    // 获取温度和湿度数据
//                    val temperature = temperatureMessages.getOrElse(i) { 0 }
//                    val humidity = humidityMessages.getOrElse(i) { 0 }
//
//                    // 写入格式化后的数据 (Elapsed Time, temperature, humidity)
//                    writer.append("$formattedTime,$temperature,$humidity\n")
//                }
//
//                writer.flush()
//                writer.close()
//
//                true // 文件保存成功
//            } catch (e: Exception) {
//                false // 文件保存失败
//            }
//
//            // 在主线程上显示Toast提示
//            withContext(Dispatchers.Main) {
//                if (result) {
//                    Toast.makeText(context, "Data saved to $fileName", Toast.LENGTH_LONG).show()
//                } else {
//                    Toast.makeText(context, "Failed to save data", Toast.LENGTH_LONG).show()
//                }
//            }
//        }
//    }
//}
