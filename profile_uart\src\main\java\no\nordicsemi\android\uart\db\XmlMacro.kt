/**
 * XML宏命令数据模型类
 *
 * 定义了单个UART宏命令的XML序列化格式：
 * 1. 基本属性：
 *    - command: 命令内容（可为空）
 *    - active: 是否激活
 *    - eol: 行结束符类型
 *    - icon: 图标类型
 *
 * 2. 扩展属性：
 *    - iconIndex: 图标索引值
 *    - eolIndex: 结束符索引值
 *
 * 3. 序列化特性：
 *    - 使用kotlinx.serialization
 *    - 支持默认值设置
 *    - 自定义XML标签和属性
 *
 * 4. 注解说明：
 *    - @XmlSerialName: 定义XML标签名
 *    - @XmlValue: 标记为XML文本内容
 *    - @XmlElement: 控制元素序列化
 *    - @XmlDefault: 设置默认值
 */

package no.nordicsemi.android.uart.db

import kotlinx.serialization.Serializable
import nl.adaptivity.xmlutil.serialization.XmlDefault
import nl.adaptivity.xmlutil.serialization.XmlElement
import nl.adaptivity.xmlutil.serialization.XmlSerialName
import nl.adaptivity.xmlutil.serialization.XmlValue
import no.nordicsemi.android.uart.data.MacroEol
import no.nordicsemi.android.uart.data.MacroIcon

@Serializable
@XmlSerialName("xml-macro")
data class XmlMacro(
	@XmlValue(true)
	var command: String? = null,

	@XmlElement(false)
	@XmlDefault("false")
	var active: Boolean = false,

	@XmlElement(false)
	@XmlDefault("LF")
	var eol: MacroEol = MacroEol.LF,

	@XmlElement(false)
	@XmlDefault("LEFT")
	var icon: MacroIcon = MacroIcon.LEFT
) {
	var iconIndex: Int
		get() = icon.ordinal
		set(value) {
			icon = MacroIcon.entries.toTypedArray()[value]
		}

	var eolIndex: Int
		get() = eol.ordinal
		set(value) {
			eol = MacroEol.entries.toTypedArray()[value]
		}
}
