/**
 * UART宏命令添加对话框
 *
 * 实现了添加新宏命令的对话框界面：
 * 1. 界面组件：
 *    - 宏命令名称输入
 *    - 命令内容输入
 *    - 行结束符选择
 *    - 确认/取消按钮
 *
 * 2. 输入验证：
 *    - 名称唯一性检查
 *    - 命令格式验证
 *    - 必填字段检查
 *
 * 3. 交互功能：
 *    - 实时输入验证
 *    - 错误提示显示
 *    - 行结束符预览
 *
 * 4. 状态管理：
 *    - 输入状态跟踪
 *    - 验证状态管理
 *    - 提交状态控制
 */

package no.nordicsemi.android.uart.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import no.nordicsemi.android.common.ui.view.RadioButtonGroup
import no.nordicsemi.android.common.ui.view.RadioButtonItem
import no.nordicsemi.android.common.ui.view.RadioGroupViewEntity
import no.nordicsemi.android.uart.R
import no.nordicsemi.android.uart.data.MacroEol
import no.nordicsemi.android.uart.data.MacroIcon
import no.nordicsemi.android.uart.data.UARTMacro
import no.nordicsemi.android.utils.EMPTY

private const val GRID_SIZE = 5

@Composable
internal fun UARTAddMacroDialog(macro: UARTMacro?, onEvent: (UARTViewEvent) -> Unit) {
    val newLineChar = rememberSaveable { mutableStateOf(macro?.newLineChar ?: MacroEol.LF) }
    val command = rememberSaveable { mutableStateOf(macro?.command ?: String.EMPTY) }
    val selectedIcon = rememberSaveable { mutableStateOf(macro?.icon ?: MacroIcon.entries.toTypedArray()[0]) }

    AlertDialog(
        onDismissRequest = { onEvent(OnEditFinish) },
        dismissButton = {
            TextButton(onClick = { onEvent(OnDeleteMacro) }) {
                Text(stringResource(id = R.string.uart_macro_dialog_delete))
            }
        },
        confirmButton = {
            TextButton(onClick = {
                onEvent(OnCreateMacro(UARTMacro(selectedIcon.value, command.value, newLineChar.value)))
            }) {
                Text(stringResource(id = R.string.uart_macro_dialog_confirm))
            }
        },
        title = {
            Text(
                text = stringResource(id = R.string.uart_macro_dialog_title),
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            LazyVerticalGrid(
                columns = GridCells.Fixed(GRID_SIZE),
                modifier = Modifier.wrapContentHeight()
            ) {
                item(span = { GridItemSpan(GRID_SIZE) }) {
                    Column {
                        NewLineCharSection(newLineChar.value) { newLineChar.value = it }

                        Spacer(modifier = Modifier.size(16.dp))
                    }
                }

                item(span = { GridItemSpan(GRID_SIZE) }) {
                    CommandInput(command)
                }

                items(20) { item ->
                    val icon = MacroIcon.create(item)
                    val background = if (selectedIcon.value == icon) {
                        MaterialTheme.colorScheme.primaryContainer
                    } else {
                        Color.Transparent
                    }

                    Image(
                        painter = painterResource(id = icon.toResId()),
                        contentDescription = stringResource(id = R.string.uart_macro_icon),
                        colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.onPrimaryContainer),
                        modifier = Modifier
                            .size(40.dp)
                            .clip(RoundedCornerShape(10.dp))
                            .clickable { selectedIcon.value = icon }
                            .background(background)
                    )
                }
            }
        }
    )
}

@Composable
private fun CommandInput(command: MutableState<String>) {
    Column {
        OutlinedTextField(
            modifier = Modifier
                .fillMaxWidth(),
            value = command.value,
            label = { Text(stringResource(id = R.string.uart_macro_dialog_command)) },
            onValueChange = {
                command.value = it
            }
        )

        Spacer(modifier = Modifier.size(16.dp))
    }
}

@Composable
private fun NewLineCharSection(checkedItem: MacroEol, onItemClick: (MacroEol) -> Unit) {
    val items = MacroEol.entries.map {
        RadioButtonItem(it.toDisplayString(), it == checkedItem)
    }
    val viewEntity = RadioGroupViewEntity(items)

    Row(verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = stringResource(id = R.string.uart_macro_dialog_eol),
            style = MaterialTheme.typography.labelLarge
        )

        RadioButtonGroup(viewEntity) {
            val i = items.indexOf(it)
            onItemClick(MacroEol.entries[i])
        }
    }
}
