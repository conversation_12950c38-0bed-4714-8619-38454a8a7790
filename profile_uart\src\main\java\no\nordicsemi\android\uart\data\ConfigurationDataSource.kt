/**
 * UART配置数据源
 *
 * 该类负责管理UART配置的持久化存储：
 * 1. 使用DataStore存储配置信息
 * 2. 提供以下功能：
 *    - 保存最后使用的配置名称
 *    - 获取最后使用的配置名称
 * 3. 使用依赖注入（Hilt）进行实例管理
 * 4. 采用单例模式确保全局唯一实例
 */

package no.nordicsemi.android.uart.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

private const val FILE = "UART_CONFIGURATION"
private const val LAST_CONFIGURATION_KEY = "LAST_CONFIGURATION"

@Singleton
internal class ConfigurationDataSource @Inject constructor(
    @ApplicationContext private val context: Context
) {

    private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = FILE)

    private val LAST_CONFIGURATION = stringPreferencesKey(LAST_CONFIGURATION_KEY)

    val lastConfigurationName = context.dataStore.data.map {
        it[LAST_CONFIGURATION]
    }

    suspend fun saveConfigurationName(name: String) {
        context.dataStore.edit {
            it[LAST_CONFIGURATION] = name
        }
    }
}
