<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) 2022, Nordic Semiconductor
  ~ All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without modification, are
  ~ permitted provided that the following conditions are met:
  ~
  ~ 1. Redistributions of source code must retain the above copyright notice, this list of
  ~ conditions and the following disclaimer.
  ~
  ~ 2. Redistributions in binary form must reproduce the above copyright notice, this list
  ~ of conditions and the following disclaimer in the documentation and/or other materials
  ~ provided with the distribution.
  ~
  ~ 3. Neither the name of the copyright holder nor the names of its contributors may be
  ~ used to endorse or promote products derived from this software without specific prior
  ~ written permission.
  ~
  ~ THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  ~ "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
  ~ TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
  ~ PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
  ~ HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  ~ SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
  ~ LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
  ~ OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  ~ OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  ~ NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
  ~ EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  -->

<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="uart_title">UART</string>

    <string name="uart_no_macros_info">Please define a macro to send command to the device.</string>

    <string name="uart_configuration_add">Add</string>
    <string name="uart_configuration_delete">Delete selected configuration.</string>
    <string name="uart_configuration_edit">Edit selected configuration.</string>

    <string name="uart_send">Send</string>
    <string name="uart_input">Input</string>
    <string name="uart_input_hint">Text to send</string>
    <string name="uart_macros">Macros</string>
    <string name="uart_output">Output</string>
    <string name="uart_configuration_picker_hint">Select configuration</string>
    <string name="uart_configuration_picker_not_selected">Not selected.</string>
    <string name="uart_configuration_picker_dialog">Select configuration</string>

    <string name="uart_command_field">Command: %s</string>

    <string name="uart_run_macro_description">Run macro</string>
    <string name="uart_delete_macro_description">Delete macro</string>

    <string name="uart_add_macro">Add macro</string>
    <string name="uart_output_info">Here will be displayed read value from GATT characteristic.</string>

    <string name="uart_configuration_dialog_title">Add configuration</string>
    <string name="uart_configuration_hint">Configuration</string>
    <string name="uart_macro_dialog_title">Add macro</string>
    <string name="uart_macro_dialog_alias">Alias</string>
    <string name="uart_macro_dialog_command">Command</string>
    <string name="uart_macro_dialog_confirm">Confirm</string>
    <string name="uart_macro_dialog_dismiss">Dismiss</string>
    <string name="uart_macro_dialog_delete">Delete</string>

    <string name="uart_macro_dialog_eol">EOL:</string>
    <string name="uart_macro_dialog_selected_eol">EOL: %s</string>
    <string name="uart_macro_dialog_lf">LF</string>
    <string name="uart_macro_dialog_cr_lf">CR + LF</string>
    <string name="uart_macro_dialog_cr">CR</string>

    <string name="uart_output_placeholder">The incoming messages will be displayed here.</string>
    <string name="uart_macro_error">Provided command cannot be empty.</string>

    <string name="uart_name">Name</string>
    <string name="uart_name_empty">Provided name cannot be empty.</string>

    <string name="uart_macro_icon">Icon representing defined command.</string>

    <string name="uart_delete_dialog_title">Delete configuration?</string>
    <string name="uart_delete_dialog_info">Are you sure that you want to delete this configuration? Your data will be irretrievably lost.</string>
    <string name="uart_delete_dialog_confirm">Confirm</string>
    <string name="uart_delete_dialog_cancel">Cancel</string>

    <string name="uart_input_macro">Click to switch between text input and macro input.</string>
    <string name="uart_clear_items">Clear items.</string>
    <string name="uart_scroll_down">Click to constantly scroll view to the latest available log.</string>
    <string name="uart_input_log">--&gt; %s</string>
    <string name="uart_output_log" tools:ignore="TypographyDashes">&lt;-- %s</string>

    <string name="uart_settings">Settings</string>
    <string name="uart_settings_button">Go to settings screen.</string>

    <string name="uart_section_data">Data</string>
    <string name="uart_section_EMG">EMG</string>
    <string name="uart_section_Body_sound">Bound_sound</string>
    <string name="uart_section_Ambient_sound">Ambient_sound</string>

    <string name="uart_section_Data_per_second">Data per second</string>

    <string name="uart_zoom_icon">Icon to zoom chart in or out</string>


</resources>
