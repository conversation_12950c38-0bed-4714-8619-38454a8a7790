package no.nordicsemi.android.uart.view

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import no.nordicsemi.android.uart.R
import no.nordicsemi.android.uart.data.FileStorageHelper
import no.nordicsemi.android.uart.data.UARTServiceData

/**
 * 按钮组件
 * 
 * 实现了保存和断开连接按钮：
 * 1. 保存按钮：
 *    - 将温湿度数据保存为CSV文件
 *    - 显示保存结果提示
 * 
 * 2. 断开连接按钮：
 *    - 断开与设备的连接
 *    - 返回上一界面
 * 
 * 3. 布局特点：
 *    - 水平排列
 *    - 居中对齐
 *    - 合理间距
 */
@Composable
internal fun ButtonSection(
    state: UARTServiceData,
    onEvent: (UARTViewEvent) -> Unit
) {
    val context = LocalContext.current

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
//        // 保存按钮
//        Button(onClick = {
//            // 调用保存功能，保存数据到CSV文件
////            FileStorageHelper.saveDataToCSV(context, state.temperatureMessages, state.humidityMessages)
//            FileStorageHelper.saveDataToCSV(context, state.bodyMessages, state.ambientMessages)
//        }) {
//            Text(text = "Save to CSV")
//        }

//        // 添加 16.dp 的间隔
//        Spacer(modifier = Modifier.width(16.dp))

        // 断开连接按钮
        Button(
            onClick = { onEvent(DisconnectEvent) }
        ) {
            Text(text = stringResource(id = R.string.disconnect))
        }
    }
} 