/**
 * UART内容视图
 *
 * 实现了UART通信的主要内容界面：
 * 1. 布局结构：
 *    - 左侧区域（1/3宽度）：
 *      * 输出区域（4/5高度）：显示通信记录
 *      * 输入区域（1/5高度）：用于发送命令
 *    - 右侧区域（2/3宽度）：
 *      * 图表显示区域：用于数据可视化
 *
 * 2. 功能组件：
 *    - OutputSection: 显示通信记录
 *    - InputSection: 命令输入
 *    - ChartView: 数据图表展示
 *
 * 3. 布局特点：
 *    - 使用权重实现比例布局
 *    - 合理的间距和填充
 *    - 响应式布局适配
 */

package no.nordicsemi.android.uart.view

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import no.nordicsemi.android.uart.data.UARTServiceData
import no.nordicsemi.android.ui.view.ScreenSection



@Composable
internal fun UARTContentView(
    state: UARTServiceData,
    onEvent: (UARTViewEvent) -> Unit
) {
    Row(
        modifier = Modifier
            .padding(5.dp)
            .fillMaxSize()
    ) {
        // 左侧部分，占屏幕的四分之一
        Column(
            modifier = Modifier
                .fillMaxHeight()
                .weight(1f)  // 1/4 的宽度
                .padding(end = 8.dp),  // 与右侧部分留一些间距
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // OutputSection 占五分之四的高度
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(3f)  // 调整权重为3
            ) {
                ScreenSection {
                    Column(modifier = Modifier.fillMaxWidth()) {
                        OutputSection(state.displayMessages, onEvent)
                    }
                }
            }

            Spacer(modifier = Modifier.size(8.dp))

            // InputSection 占五分之一的高度
            Box(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                InputSection(onEvent = onEvent)
            }

            Spacer(modifier = Modifier.size(8.dp))

            // 添加数据记录视图
            DataRecorderView(state, onEvent)

            Spacer(modifier = Modifier.size(8.dp))

            // 添加按钮组件
            ButtonSection(state, onEvent)

        }

        // 右侧部分，占屏幕的四分之三
        Column(
            modifier = Modifier
                .fillMaxHeight()
                .weight(3f)  // 3/4 的宽度
                .padding(start = 8.dp),  // 与左侧部分留一些间距
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 第一个图表
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                ChartView_EMG(state, onEvent)
            }

            Spacer(modifier = Modifier.size(8.dp))

            // 第二个图表
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                ChartView_Body(state, onEvent)
            }

            Spacer(modifier = Modifier.size(8.dp))

            // 第三个图表
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                ChartView_Ambient(state, onEvent)
            }
        }
    }
}
