{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:base", "group:all", ":dependencyDashboard", "schedule:daily"], "commitMessageExtra": "{{{currentValue}}} to {{#if isPinDigest}}{{{newDigestShort}}}{{else}}{{#if isMajor}}{{prettyNewMajor}}{{else}}{{#if isSingleVersion}}{{prettyNewVersion}}{{else}}{{#if newValue}}{{{newValue}}}{{else}}{{{newDigestShort}}}{{/if}}{{/if}}{{/if}}{{/if}}", "packageRules": [{"matchPackagePatterns": ["androidx.compose.compiler:compiler"], "groupName": "kotlin"}, {"matchPackagePatterns": ["org.jetbrains.kotlin.*"], "groupName": "kotlin"}, {"matchPackagePatterns": ["com.google.devtools.ksp"], "groupName": "kotlin"}]}