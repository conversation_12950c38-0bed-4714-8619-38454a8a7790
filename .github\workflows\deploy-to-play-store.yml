name: Deploy to Play Store Internal
on:
  push:
    tags:
      - '*'
  workflow_dispatch:
jobs:
  restorePassword:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-java@v4
        with:
          distribution: 'corretto'
          java-version: '17'
      - shell: bash
        env:
          KEYSTORE_ALIAS: ${{ secrets.KEYSTORE_ALIAS }}
          KEYSTORE_KEY_PSWD: ${{ secrets.KEYSTORE_KEY_PSWD }}
          KEYSTORE_PSWD: ${{ secrets.KEYSTORE_PSWD }}
          GPG_SIGNING_KEY: ${{ secrets.GPG_SIGNING_KEY }}
          GPG_PASSWORD: ${{ secrets.GPG_PASSWORD }}
          OSSR_USERNAME: ${{ secrets.OSSR_USERNAME }}
          OSSR_PASSWORD: ${{ secrets.OSSR_PASSWORD }}
          SONATYPE_STATING_PROFILE_ID: ${{ secrets.SONATYPE_STATING_PROFILE_ID }}
        run: |
          pwd
          echo "${{ secrets.GOOGLE_SERVICES }}" | base64 --decode > app/google-services.json
          echo "${{ secrets.KEYSTORE_FILE }}" > keystore.asc
          gpg -d --passphrase "${{ secrets.KEYSTORE_FILE_PSWD }}" --batch keystore.asc > keystore
          echo "${{ secrets.GPG_FILE }}" > sec.gpg.asc
          gpg -d --passphrase "${{ secrets.GPG_FILE_PSWD }}" --batch sec.gpg.asc > sec.gpg
          echo "${{ secrets.API_KEY_FILE }}" > fastlane-api.json.asc
          gpg -d --passphrase "${{ secrets.API_KEY_FILE_PSWD }}" --batch fastlane-api.json.asc > fastlane-api.json
          fastlane deployInternal
