/**
 * UART配置添加对话框
 *
 * 实现了添加新UART配置的对话框界面：
 * 1. 界面组件：
 *    - 配置名称输入
 *    - 命令内容输入
 *    - 确认/取消按钮
 *    - 错误提示
 *
 * 2. 输入验证：
 *    - 名称唯一性检查
 *    - 必填字段验证
 *    - 格式验证
 *
 * 3. 交互功能：
 *    - 实时输入验证
 *    - 错误提示显示
 *    - 提交处理
 *
 * 4. 状态管理：
 *    - 输入状态
 *    - 验证状态
 *    - 提交状态
 */

package no.nordicsemi.android.uart.view

import androidx.compose.foundation.layout.Column
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.res.stringResource
import no.nordicsemi.android.uart.R
import no.nordicsemi.android.utils.EMPTY

@Composable
internal fun UARTAddConfigurationDialog(onEvent: (UARTViewEvent) -> Unit, onDismiss: () -> Unit) {
    val name = rememberSaveable { mutableStateOf(String.EMPTY) }
    val isError = rememberSaveable { mutableStateOf(false) }

    AlertDialog(
        onDismissRequest = { onDismiss() },
        title = { Text(stringResource(id = R.string.uart_configuration_dialog_title)) },
        text = { NameInput(name, isError) },
        confirmButton = {
            TextButton(onClick = {
                if (isNameValid(name.value)) {
                    onDismiss()
                    onEvent(OnAddConfiguration(name.value))
                } else {
                    isError.value = true
                }
            }) {
                Text(stringResource(id = R.string.uart_macro_dialog_confirm))
            }
        },
        dismissButton = {
            TextButton(onClick = { onDismiss() }) {
                Text(stringResource(id = R.string.uart_macro_dialog_dismiss))
            }
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun NameInput(
    name: MutableState<String>,
    isError: MutableState<Boolean>
) {
    Column {

        OutlinedTextField(
            value = name.value,
            label = { Text(stringResource(id = R.string.uart_configuration_hint)) },
            singleLine = true,
            onValueChange = {
                isError.value = false
                name.value = it
            }
        )

        val errorText = if (isError.value) {
            stringResource(id = R.string.uart_name_empty)
        } else {
            String.EMPTY
        }

        Text(
            text = errorText,
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.error
        )
    }
}

private fun isNameValid(name: String): Boolean {
    return name.isNotBlank()
}
