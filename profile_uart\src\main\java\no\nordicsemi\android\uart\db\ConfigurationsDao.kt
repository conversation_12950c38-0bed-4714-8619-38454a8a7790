/**
 * UART配置数据访问对象（DAO）
 *
 * 定义了对UART配置表的数据库操作接口：
 * 1. 查询操作：
 *    - load(): 加载所有配置
 *    - 返回Flow类型实现响应式数据流
 *
 * 2. 插入操作：
 *    - insert(): 插入或更新配置
 *    - 使用REPLACE策略处理冲突
 *
 * 3. 删除操作：
 *    - delete(): 根据名称删除配置
 *
 * 4. 特点：
 *    - 使用Room的@Dao注解
 *    - 支持协程的异步操作
 *    - 使用SQL查询语句
 */

package no.nordicsemi.android.uart.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import kotlinx.coroutines.flow.Flow

@Dao
internal interface ConfigurationsDao {

    @Query("SELECT * FROM configurations")
    fun load(): Flow<List<Configuration>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(configuration: Configuration)

    @Query("DELETE FROM configurations WHERE name = :name")
    suspend fun delete(name: String)
}
