/**
 * UART配置数据模型
 *
 * 定义了UART通信配置的数据结构：
 * 1. 配置基本信息：
 *    - id: 配置唯一标识符
 *    - name: 配置名称
 *
 * 2. 宏命令列表：
 *    - 固定包含9个宏命令位置
 *    - 每个位置可以为空
 *    - 使用UARTMacro类型存储具体命令
 *
 * 3. 验证规则：
 *    - 确保宏命令列表始终包含9个位置
 *    - 如果数量不足会抛出异常
 */

package no.nordicsemi.android.uart.data

private const val MACROS_SIZES = 9

data class UARTConfiguration(
    val id: Int?,
    val name: String,
    val macros: List<UARTMacro?> = List<UARTMacro?>(9) { null }
) {

    init {
        if (macros.size < 9) {
            throw IllegalArgumentException("Macros should always have 9 positions.")
        }
    }
}
