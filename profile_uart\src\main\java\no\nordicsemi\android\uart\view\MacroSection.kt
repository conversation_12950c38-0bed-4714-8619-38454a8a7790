/**
 * 宏命令管理界面
 *
 * 实现了UART宏命令的管理功能：
 * 1. 界面布局：
 *    - 宏命令列表：显示所有已保存的宏命令
 *    - 添加按钮：创建新的宏命令
 *    - 编辑功能：修改现有宏命令
 *
 * 2. 宏命令功能：
 *    - 创建宏命令
 *    - 编辑宏命令
 *    - 删除宏命令
 *    - 执行宏命令
 *
 * 3. 列表显示：
 *    - 宏命令名称
 *    - 命令内容预览
 *    - 执行按钮
 *    - 编辑/删除选项
 *
 * 4. 状态管理：
 *    - 宏命令列表状态
 *    - 编辑状态
 *    - 执行状态
 *    - 错误处理
 */

package no.nordicsemi.android.uart.view

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import no.nordicsemi.android.uart.R
import no.nordicsemi.android.ui.view.ScreenSection
import no.nordicsemi.android.ui.view.SectionTitle

@Composable
internal fun MacroSection(viewState: UARTViewState, onEvent: (UARTViewEvent) -> Unit) {
    val showAddDialog = rememberSaveable { mutableStateOf(false) }
    val showDeleteDialog = rememberSaveable { mutableStateOf(false) }

    if (showAddDialog.value) {
        UARTAddConfigurationDialog(onEvent) { showAddDialog.value = false }
    }

    if (showDeleteDialog.value) {
        DeleteConfigurationDialog(onEvent) { showDeleteDialog.value = false }
    }

    if (viewState.showEditDialog) {
        UARTAddMacroDialog(viewState.selectedMacro) { onEvent(it) }
    }

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
            .heightIn(min = 400.dp)
    ) {
        ScreenSection {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                SectionTitle(
                    resId = R.drawable.ic_macro,
                    title = stringResource(R.string.uart_macros),
                    menu = {
                        viewState.selectedConfiguration?.let {
                            if (!viewState.isConfigurationEdited) {
                                IconButton(onClick = { onEvent(OnEditConfiguration) }) {
                                    Icon(
                                        Icons.Default.Edit,
                                        stringResource(id = R.string.uart_configuration_edit)
                                    )
                                }
                            } else {
                                IconButton(onClick = { onEvent(OnEditConfiguration) }) {
                                    Icon(
                                        painterResource(id = R.drawable.ic_pencil_off),
                                        stringResource(id = R.string.uart_configuration_edit)
                                    )
                                }
                            }
                            IconButton(onClick = { showDeleteDialog.value = true }) {
                                Icon(
                                    Icons.Default.Delete,
                                    stringResource(id = R.string.uart_configuration_delete)
                                )
                            }
                        }
                    }
                )

                Spacer(modifier = Modifier.height(16.dp))

                Row {
                    Box(modifier = Modifier.weight(1f)) {
                        UARTConfigurationPicker(viewState, onEvent)
                    }

                    Spacer(modifier = Modifier.size(16.dp))

                    Button(onClick = { showAddDialog.value = true }) {
                        Text(stringResource(id = R.string.uart_configuration_add))
                    }
                }

                viewState.selectedConfiguration?.let {
                    Spacer(modifier = Modifier.height(16.dp))

                    UARTMacroView(it, viewState.isConfigurationEdited, onEvent)
                }
            }
        }
    }
}

@Composable
private fun DeleteConfigurationDialog(onEvent: (UARTViewEvent) -> Unit, onDismiss: () -> Unit) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = stringResource(id = R.string.uart_delete_dialog_title),
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            Text(text = stringResource(id = R.string.uart_delete_dialog_info))
        },
        confirmButton = {
            TextButton(onClick = {
                onDismiss()
                onEvent(OnDeleteConfiguration)
            }) {
                Text(text = stringResource(id = R.string.uart_delete_dialog_confirm))
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(text = stringResource(id = R.string.uart_delete_dialog_cancel))
            }
        }
    )
}
