/**
 * UART数据解析工具
 *
 * 提供了字符串处理的扩展函数：
 * 1. parseWithNewLineChar：
 *    - 根据指定的结束符类型处理字符串
 *    - 支持三种结束符转换：
 *      * LF (\n)：保持原样
 *      * CR_LF (\r\n)：将\n转换为\r\n
 *      * CR (\r)：将\n转换为\r
 *
 * 该工具用于确保发送的命令具有正确的行结束符格式
 */

package no.nordicsemi.android.uart.data

fun String.parseWithNewLineChar(newLineChar: MacroEol): String {
    return when (newLineChar) {
        MacroEol.LF -> this
        MacroEol.CR_LF -> this.replace("\n", "\r\n")
        MacroEol.CR -> this.replace("\n", "\r")
    }
}
