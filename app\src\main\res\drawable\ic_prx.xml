<!--
  ~ Copyright (c) 2022, Nordic Semiconductor
  ~ All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without modification, are
  ~ permitted provided that the following conditions are met:
  ~
  ~ 1. Redistributions of source code must retain the above copyright notice, this list of
  ~ conditions and the following disclaimer.
  ~
  ~ 2. Redistributions in binary form must reproduce the above copyright notice, this list
  ~ of conditions and the following disclaimer in the documentation and/or other materials
  ~ provided with the distribution.
  ~
  ~ 3. Neither the name of the copyright holder nor the names of its contributors may be
  ~ used to endorse or promote products derived from this software without specific prior
  ~ written permission.
  ~
  ~ THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  ~ "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
  ~ TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
  ~ PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
  ~ HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  ~ <PERSON>ECIA<PERSON>, E<PERSON>EMPLAR<PERSON>, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
  ~ LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
  ~ OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  ~ OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  ~ NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
  ~ EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="80dp"
    android:height="80dp"
    android:viewportWidth="1024"
    android:viewportHeight="1024">
    <path
        android:fillColor="#00B3DC"
        android:pathData="M176,180.1m-70.2,0a70.2,70.2 0,1 1,140.4 0a70.2,70.2 0,1 1,-140.4 0" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M152.7,433.7c-13.6,0 -25.7,-9.8 -28.1,-23.7c-2.7,-15.5 7.7,-30.3 23.3,-32.9c73.8,-12.7 123.5,-35.2 161.3,-72.9c44.4,-44.4 60.8,-101.4 71.1,-151.3c3.2,-15.4 18.2,-25.4 33.6,-22.2c15.4,3.2 25.4,18.2 22.2,33.6c-7.7,37.7 -16.8,67.8 -28.6,94.5c-14.8,33.4 -33.7,61.4 -58,85.7c-59.7,59.7 -133.9,78.8 -191.9,88.8C155.9,433.6 154.3,433.7 152.7,433.7z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M154.8,584.1c-0.8,0 -1.7,0 -2.5,0c-15.7,-0.1 -28.4,-12.9 -28.4,-28.7c0.1,-15.7 12.8,-28.4 28.5,-28.4c0.1,0 0.1,0 0.2,0c0.7,0 1.5,0 2.2,0c104,0 199.4,-38.2 268.9,-107.6c67.6,-67.6 105.8,-160.4 107.5,-261.4c0.3,-15.6 13,-28 28.5,-28c0.2,0 0.3,0 0.5,0c15.7,0.3 28.3,13.3 28,29c-1,56.7 -11.9,111 -32.3,161.6c-21.2,52.5 -52.2,99.3 -92,139.1C383.8,540 274.1,584.1 154.8,584.1z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M848,843.9m-70.2,0a70.2,70.2 0,1 1,140.4 0a70.2,70.2 0,1 1,-140.4 0" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M615.9,893.9c-1.9,0 -3.8,-0.2 -5.8,-0.6c-15.4,-3.2 -25.4,-18.2 -22.2,-33.6c7.7,-37.7 16.8,-67.8 28.6,-94.5c14.8,-33.4 33.7,-61.5 58,-85.7c59.7,-59.7 133.9,-78.8 191.9,-88.8c15.5,-2.7 30.3,7.7 32.9,23.3c2.7,15.5 -7.7,30.3 -23.3,32.9c-73.8,12.7 -123.5,35.2 -161.3,72.9c-44.4,44.4 -60.8,101.4 -71.1,151.3C641,884.6 629.2,893.9 615.9,893.9z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M464.3,893.9c-0.2,0 -0.3,0 -0.5,0c-15.7,-0.3 -28.3,-13.3 -28,-29c1,-56.7 11.9,-111 32.3,-161.6c21.2,-52.5 52.2,-99.3 92,-139.1c80.8,-80.8 191.5,-124.9 311.7,-124.3c15.7,0.1 28.4,12.9 28.4,28.7c-0.1,15.7 -12.8,28.4 -28.5,28.4c-0.1,0 -0.1,0 -0.2,0c-104.9,-0.5 -201.2,37.7 -271.1,107.6c-67.6,67.6 -105.8,160.4 -107.5,261.4C492.5,881.5 479.8,893.9 464.3,893.9z" />
</vector>
