/**
 * UART视图事件定义
 *
 * 定义了UART界面中所有可能发生的用户交互事件：
 * 1. 连接事件：
 *    - 连接请求
 *    - 断开连接
 *    - 重新连接
 *
 * 2. 数据操作：
 *    - 发送命令
 *    - 清除输出
 *    - 执行宏命令
 *
 * 3. 配置管理：
 *    - 添加配置
 *    - 删除配置
 *    - 修改配置
 *
 * 4. 界面控制：
 *    - 切换视图
 *    - 缩放控制
 *    - 导航操作
 *
 * 5. 宏命令管理：
 *    - 添加宏命令
 *    - 删除宏命令
 *    - 执行宏命令
 */
package no.nordicsemi.android.uart.view

import no.nordicsemi.android.uart.data.MacroEol
import no.nordicsemi.android.uart.data.UARTConfiguration
import no.nordicsemi.android.uart.data.UARTMacro

internal sealed class UARTViewEvent

internal data class OnEditMacro(val position: Int) : UARTViewEvent()
internal data class OnCreateMacro(val macro: UARTMacro) : UARTViewEvent()
internal object OnDeleteMacro : UARTViewEvent()
internal object OnEditFinish : UARTViewEvent()

internal data class OnConfigurationSelected(val configuration: UARTConfiguration) : UARTViewEvent()
internal data class OnAddConfiguration(val name: String) : UARTViewEvent()
internal object OnEditConfiguration : UARTViewEvent()
internal object OnDeleteConfiguration : UARTViewEvent()
internal data class OnRunMacro(val macro: UARTMacro) : UARTViewEvent()
internal data class OnRunInput(val text: String, val newLineChar: MacroEol) : UARTViewEvent()

internal object ClearOutputItems : UARTViewEvent()
internal object DisconnectEvent : UARTViewEvent()

internal object NavigateUp : UARTViewEvent()
internal object OpenLogger : UARTViewEvent()

internal object MacroInputSwitchClick : UARTViewEvent()

//修改
internal object SwitchZoomEvent : UARTViewEvent()
