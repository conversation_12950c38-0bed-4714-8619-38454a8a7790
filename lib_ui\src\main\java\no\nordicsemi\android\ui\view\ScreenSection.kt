/*
 * ScreenSection.kt
 *
 * 该文件定义了一个屏幕分区组件，用于在应用程序中创建独立的内容区域。
 * 主要功能：
 * 1. 提供一个带有卡片样式的内容容器
 * 2. 支持自定义内容通过content参数传入
 * 3. 使用Material Design的OutlinedCard组件
 * 4. 包含预览功能，方便开发时查看组件效果
 */

package no.nordicsemi.android.ui.view

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

@Composable
fun ScreenSection(content: @Composable () -> Unit) {
    OutlinedCard {
        Column(modifier = Modifier.padding(1.dp)) {
            content()
        }
    }
}


/*
预览
 */
@Preview(showBackground = true)
@Composable
fun ScreenSectionPreview() {
    ScreenSection {
        Text(text = "This is a screen section")
    }
}
