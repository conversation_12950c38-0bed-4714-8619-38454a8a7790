/*
 * BackButton.kt
 *
 * 该文件定义了一个可重用的返回按钮组件，用于在应用程序中实现导航功能。
 * 主要功能：
 * 1. 提供一个标准的返回按钮UI组件
 * 2. 支持自定义导航回调函数
 * 3. 包含预览功能，方便在开发时查看组件效果
 */

package no.nordicsemi.android.ui.view

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import no.nordicsemi.android.ui.R

@Composable
fun NavigateUpButton(navigateUp: () -> Unit) {
    Button(
        onClick = { navigateUp() },
        modifier = Modifier.padding(top = 16.dp)
    ) {
        Text(text = stringResource(id = R.string.go_up))
    }
}

/*
预览
 */
@Preview(showBackground = true)
@Composable
fun NavigateUpButtonPreview() {
    NavigateUpButton(navigateUp = {})
}
