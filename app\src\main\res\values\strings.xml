<!--
  ~ Copyright (c) 2022, Nordic Semiconductor
  ~ All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without modification, are
  ~ permitted provided that the following conditions are met:
  ~
  ~ 1. Redistributions of source code must retain the above copyright notice, this list of
  ~ conditions and the following disclaimer.
  ~
  ~ 2. Redistributions in binary form must reproduce the above copyright notice, this list
  ~ of conditions and the following disclaimer in the documentation and/or other materials
  ~ provided with the distribution.
  ~
  ~ 3. Neither the name of the copyright holder nor the names of its contributors may be
  ~ used to endorse or promote products derived from this software without specific prior
  ~ written permission.
  ~
  ~ THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  ~ "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
  ~ TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
  ~ PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
  ~ HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  ~ <PERSON>ECIA<PERSON>, E<PERSON>EMPLAR<PERSON>, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
  ~ LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
  ~ OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  ~ OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  ~ NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
  ~ EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  -->

<resources>
    <string name="csc_module">CSC</string>
    <string name="csc_module_full">Cycling Speed and Cadence</string>
    <string name="hrs_module">HRS</string>
    <string name="hrs_module_full">Heart Rate</string>
    <string name="gls_module">GLS</string>
    <string name="gls_module_full">Glucose</string>
    <string name="hts_module">HTS</string>
    <string name="hts_module_full">Health Thermometer</string>
    <string name="bps_module">BPS</string>
    <string name="bps_module_full">Blood Pressure</string>
    <string name="rscs_module">RSCS</string>
    <string name="rscs_module_full">Running Speed and Cadence</string>
    <string name="prx_module">PRX</string>
    <string name="prx_module_full">Proximity</string>
    <string name="cgm_module">CGMS</string>
    <string name="cgm_module_full">Continuous Glucose</string>
    <string name="uart_module">UART</string>
    <string name="uart_module_full">Universal Asynchronous Receiver/Transmitter (UART)</string>
    <string name="dfu_module">DFU</string>
    <string name="dfu_module_full">Device Firmware Update</string>
    <string name="dfu_module_info">Open DFU application.</string>
    <string name="dfu_module_install">Download from Google Play.</string>
    <string name="logger_module">nRF Logger</string>
    <string name="logger_module_full">nRF Logger</string>
    <string name="logger_module_info">Open nRF Logger application.</string>

    <string name="viewmodel_profiles">ViewModel profiles</string>
    <string name="service_profiles">Service profiles</string>
    <string name="utils_services">Utils services</string>

    <string name="running_profile_icon">Icon indicating if the profile is running</string>
</resources>
