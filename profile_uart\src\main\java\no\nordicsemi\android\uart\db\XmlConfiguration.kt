/**
 * XML配置数据模型类
 *
 * 定义了UART配置的XML序列化格式：
 * 1. 配置结构：
 *    - name: 配置名称
 *    - commands: 命令列表（固定9个命令）
 *
 * 2. 序列化特性：
 *    - 使用kotlinx.serialization进行XML序列化
 *    - 支持XML标签自定义
 *    - 忽略空白字符
 *
 * 3. 数据验证：
 *    - 确保命令列表长度为9
 *    - 提供equals和hashCode实现
 *
 * 4. 注解说明：
 *    - @Serializable: 支持序列化
 *    - @XmlSerialName: 定义XML标签名
 *    - @XmlElement: 控制元素序列化行为
 */

package no.nordicsemi.android.uart.db

import kotlinx.serialization.Serializable
import nl.adaptivity.xmlutil.serialization.XmlElement
import nl.adaptivity.xmlutil.serialization.XmlIgnoreWhitespace
import nl.adaptivity.xmlutil.serialization.XmlSerialName

@Serializable
@XmlSerialName("xml-configuration")
@XmlIgnoreWhitespace
data class XmlConfiguration(
	@XmlElement(false)
	var name: String = "Unnamed",

	@XmlElement(true)
	var commands: Commands = Commands()
) {

	@Serializable
	@XmlSerialName("commands")
	data class Commands(
		var commands: Array<XmlMacro> = arrayOf(
			XmlMacro(),
			XmlMacro(),
			XmlMacro(),
			XmlMacro(),
			XmlMacro(),
			XmlMacro(),
			XmlMacro(),
			XmlMacro(),
			XmlMacro()
		),

		@XmlElement(false)
		val length: Int = commands.size,
	) {
		init {
			require(commands.size == COMMANDS_COUNT) { "A Macro must have 9 commands" }
		}

		override fun equals(other: Any?): Boolean {
			if (this === other) return true
			if (javaClass != other?.javaClass) return false

			other as Commands

			if (length != other.length) return false
			if (!commands.contentEquals(other.commands)) return false

			return true
		}

		override fun hashCode(): Int {
			var result = length
			result = 31 * result + commands.contentHashCode()
			return result
		}
	}

	companion object {
		const val COMMANDS_COUNT = 9
	}
}
