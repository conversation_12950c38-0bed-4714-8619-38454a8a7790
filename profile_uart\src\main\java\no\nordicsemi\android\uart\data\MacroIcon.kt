/**
 * UART宏命令图标枚举
 *
 * 定义了UART通信界面中使用的图标类型：
 * 1. 方向控制图标：
 *    - LEFT, UP, RIGHT, DOWN
 *
 * 2. 媒体控制图标：
 *    - REW (后退)
 *    - PLAY (播放)
 *    - PAUSE (暂停)
 *    - STOP (停止)
 *    - FWD (前进)
 *
 * 3. 功能图标：
 *    - SETTINGS (设置)
 *    - INFO (信息)
 *
 * 4. 数字图标：
 *    - NUMBER_1 到 NUMBER_9
 *
 * 每个图标都有对应的索引值，用于在配置和UI中标识使用的图标类型
 */


package no.nordicsemi.android.uart.data

enum class MacroIcon(val index: Int) {
    LEFT(0),
    UP(1),
    RIGHT(2),
    DOWN(3),
    SETTINGS(4),
    REW(5),
    PLAY(6),
    PAUSE(7),
    STOP(8),
    FWD(9),
    INFO(10),
    NUMBER_1(11),
    NUMBER_2(12),
    NUMBER_3(13),
    NUMBER_4(14),
    NUMBER_5(15),
    NUMBER_6(16),
    NUMBER_7(17),
    NUMBER_8(18),
    NUMBER_9(19);

    companion object {
        fun create(index: Int): MacroIcon {
            return entries.firstOrNull { it.index == index }
                ?: throw IllegalArgumentException("Cannot create MacroIcon for index: $index")
        }
    }
}
