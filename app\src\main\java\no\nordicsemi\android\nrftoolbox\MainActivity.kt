/*
 * Copyright (c) 2022, Nordic Semiconductor
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are
 * permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this list of
 * conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice, this list
 * of conditions and the following disclaimer in the documentation and/or other materials
 * provided with the distribution.
 *
 * 3. Neither the name of the copyright holder nor the names of its contributors may be
 * used to endorse or promote products derived from this software without specific prior
 * written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
 * TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
 * PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * <PERSON>ECIAL, <PERSON><PERSON>EM<PERSON>AR<PERSON>, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
 * OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 * OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

package no.nordicsemi.android.nrftoolbox

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import dagger.hilt.android.AndroidEntryPoint
import no.nordicsemi.android.common.analytics.view.AnalyticsPermissionRequestDialog
import no.nordicsemi.android.common.navigation.NavigationView
import no.nordicsemi.android.common.theme.NordicActivity
import no.nordicsemi.android.common.theme.NordicTheme
//import no.nordicsemi.android.gls.GLSDestination
import no.nordicsemi.android.nrftoolbox.repository.ActivitySignals
import no.nordicsemi.android.toolbox.scanner.ScannerDestination
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : NordicActivity() {

    @Inject
    lateinit var activitySignals: ActivitySignals

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            NordicTheme {
                Surface(
                    color = MaterialTheme.colorScheme.surface,
                    modifier = Modifier.fillMaxSize()
                ) {
//                    NavigationView(HomeDestinations + ProfileDestinations + ScannerDestination + GLSDestination)
                    NavigationView(HomeDestinations + ProfileDestinations + ScannerDestination)
                }

                AnalyticsPermissionRequestDialog()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        activitySignals.onResume()
    }
}

/*
预览界面
 */

//@Composable
//fun MainContent() {
//    NordicTheme {
//        Surface(
//            color = MaterialTheme.colorScheme.surface,
//            modifier = Modifier.fillMaxSize()
//        ) {
//            // 使用占位符来代替真实的 NavigationView 依赖
//            PlaceholderNavigationView()
//        }
//
//        // 假设 AnalyticsPermissionRequestDialog 是 UI 的一部分，你也可以使用占位符
//        PlaceholderAnalyticsDialog()
//    }
//}
//
//@Composable
//fun PlaceholderNavigationView() {
//    // 这是一个占位符，用来代替实际的 NavigationView 内容
//    Surface(
//        color = MaterialTheme.colorScheme.primary,
//        modifier = Modifier.fillMaxSize()
//    ) {
//        // 在这里显示一些简单的内容，用于预览
//    }
//}
//
//@Composable
//fun PlaceholderAnalyticsDialog() {
//    // 这是一个占位符，用来代替 AnalyticsPermissionRequestDialog
//}
//
//
//@Preview(showBackground = true)
//@Composable
//fun PreviewMainContent() {
//    MainContent()
//}



