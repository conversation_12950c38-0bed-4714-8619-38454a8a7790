/**
 * UART配置数据库实体类
 *
 * 使用Room数据库框架定义的配置表实体：
 * 1. 表名：configurations
 *
 * 2. 字段说明：
 *    - _id: 主键，自动生成
 *    - name: 配置名称
 *    - xml: XML格式的配置内容
 *    - deleted: 软删除标记（0表示未删除）
 *
 * 3. 注解说明：
 *    - @Entity: 标记为数据库表
 *    - @PrimaryKey: 定义主键
 *    - @ColumnInfo: 定义列信息
 */

package no.nordicsemi.android.uart.db

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "configurations")
internal data class Configuration(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "_id") val _id: Int?,
    @ColumnInfo(name = "name") val name: String,
    @ColumnInfo(name = "xml") val xml: String,
    @ColumnInfo(name = "deleted", defaultValue = "0") val deleted: Int
)
