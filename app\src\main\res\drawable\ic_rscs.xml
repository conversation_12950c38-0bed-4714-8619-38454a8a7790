<!--
  ~ Copyright (c) 2022, Nordic Semiconductor
  ~ All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without modification, are
  ~ permitted provided that the following conditions are met:
  ~
  ~ 1. Redistributions of source code must retain the above copyright notice, this list of
  ~ conditions and the following disclaimer.
  ~
  ~ 2. Redistributions in binary form must reproduce the above copyright notice, this list
  ~ of conditions and the following disclaimer in the documentation and/or other materials
  ~ provided with the distribution.
  ~
  ~ 3. Neither the name of the copyright holder nor the names of its contributors may be
  ~ used to endorse or promote products derived from this software without specific prior
  ~ written permission.
  ~
  ~ THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  ~ "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
  ~ TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
  ~ PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
  ~ HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  ~ <PERSON>ECIA<PERSON>, E<PERSON>EMPLAR<PERSON>, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
  ~ LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
  ~ OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  ~ OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  ~ NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
  ~ EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="80dp"
    android:height="80dp"
    android:viewportWidth="1024"
    android:viewportHeight="1024">
    <path
        android:fillColor="#00B3DC"
        android:pathData="M982.9,706.2c-10.8,-37.3 -61.8,-59.1 -126.3,-86.7c-26.1,-11.2 -53.1,-22.7 -73.8,-34.3c-26.6,-14.9 -30.8,-23.3 -31.4,-24.8C730,504.6 641.7,260.1 634.7,237.4c-6.8,-22.1 -23.1,-38.5 -43.7,-43.8c-12.9,-3.3 -38.5,-5 -65.7,21.3c-16.6,16 -30.5,38.5 -40.2,57.1c-4.8,-3.8 -9.9,-7.9 -15,-12.3c-37.1,-31.1 -43.9,-44 -44.7,-46c-1.7,-8.8 -0.2,-25.9 1,-41c2.7,-31.8 5.2,-61.8 -11.7,-80.3c-15,-16.5 -41.3,-23.4 -72.2,-19.1c-31.8,4.4 -67.1,19.7 -105,45.4c-19.8,13.4 -36.3,27 -50,39.8c-0.6,0.5 -1.2,1.1 -1.8,1.7c-0.1,0.1 -0.3,0.3 -0.4,0.4c-14.2,13.4 -25.2,26 -33.6,36.8c-36.3,42.6 -64,85 -79.2,109.6c-14.6,23.8 -17.6,52.5 -8.3,78.8c9.5,26.7 30.1,66.5 74.6,103.9c91,76.7 198.5,176.9 227.5,227.9c19.9,35.1 48.2,80.3 90.2,118.7c52.8,48.3 114.7,74.2 184,77c38.5,1.6 77.8,2.8 115,2.8c33,0 64.3,-1 92,-3.7c50.6,-4.9 118.8,-17.4 127.9,-69c2.8,-15.7 4.7,-37.3 6.1,-57.8C984.7,758.2 987.6,722.4 982.9,706.2zM269.4,165.8c58,-39.4 91.9,-38.7 101.4,-35.5c1.1,8.3 -0.4,26.3 -1.4,37.6c-1.7,20.3 -3.5,41.4 0.1,58.3c2.8,13.1 12.3,33.5 61.1,74.9c24.4,20.7 47.7,37.4 48.7,38.1c7.2,5.1 16.3,6.6 24.7,4.1c8.4,-2.5 15.2,-8.8 18.4,-17c5.2,-13.3 22.9,-51.9 42.3,-70.6c6.8,-6.6 11.1,-7.3 11.9,-7.1c0.5,0.1 2.4,1.7 3.5,5.3c3.1,10.2 18.4,53.4 37.2,105.6l-73.1,12.9c-10.3,1.8 -17.2,11.7 -15.4,22c1.6,9.2 9.6,15.7 18.7,15.7c1.1,0 2.2,-0.1 3.3,-0.3l79.6,-14.1c5.3,14.8 10.8,29.9 16.2,44.7l-74.3,13.1c-10.3,1.8 -17.2,11.7 -15.4,22c1.6,9.2 9.6,15.7 18.7,15.7c1.1,0 2.2,-0.1 3.3,-0.3l80.9,-14.3c5.8,15.8 11.3,31 16.4,44.7L600.4,535c-10.3,1.8 -17.2,11.7 -15.4,22c1.6,9.2 9.6,15.7 18.7,15.7c1.1,0 2.2,-0.1 3.3,-0.3l82.6,-14.6c3.5,9.5 6.5,17.3 8.6,22.9c7.4,19.4 25.5,36.6 56.7,54.1c23.4,13.1 51.8,25.3 79.3,37c24.5,10.5 47.7,20.4 66.1,30.4c19.3,10.4 25.5,17 27.4,19.3c-0.1,0.5 -0.1,1.1 -0.2,1.6c-0.1,1.4 -0.2,3.5 -0.4,8.6c-0.3,6.3 -0.7,15.8 -1.3,26.9c-63.1,9.8 -182.8,18.5 -260.2,2.1c-98.2,-20.7 -146.9,-106.7 -179.2,-163.6l-1.5,-2.7C448.5,530.4 305.1,389 239.1,333.4c-40.1,-33.8 -51.7,-67.3 -55.1,-83.3c2.4,-3.9 6,-9.4 11.1,-16.1c9.5,-11.2 19.7,-22.4 30.4,-33.2C237.3,189.7 251.8,177.8 269.4,165.8zM841.8,855.7c-55.5,5.4 -128.5,3.5 -199.2,0.6c-119.9,-4.8 -184.2,-91.7 -226.9,-166.9c-40,-70.4 -181.4,-193.6 -240.3,-243.3c-34.7,-29.2 -50.5,-59.3 -57.6,-79.4c-3.5,-10 -2.4,-20.9 3.1,-29.9c5.6,-9.1 13,-20.7 22,-33.9c13.3,26.9 33.7,52.2 59.5,74c70.8,59.7 203.3,193.6 233,245.7l1.5,2.7c16.9,29.8 40.1,70.7 73.9,107.2c41,44.3 89.2,72.6 143.1,84c35.8,7.6 77.7,10.3 119.1,10.3c55.7,0 110.6,-5 148.4,-10.1c-0.7,6.2 -1.5,11.8 -2.3,16.7C919,833.7 912,848.9 841.8,855.7z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M54.9,666.5h87.5c10.5,0 19,-8.5 19,-19s-8.5,-19 -19,-19H54.9c-10.5,0 -19,8.5 -19,19S44.4,666.5 54.9,666.5z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M54.9,782.2H239c10.5,0 19,-8.5 19,-19s-8.5,-19 -19,-19H54.9c-10.5,0 -19,8.5 -19,19S44.4,782.2 54.9,782.2z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M364.3,859.9H54.9c-10.5,0 -19,8.5 -19,19s8.5,19 19,19h309.4c10.5,0 19,-8.5 19,-19S374.8,859.9 364.3,859.9z" />
</vector>
