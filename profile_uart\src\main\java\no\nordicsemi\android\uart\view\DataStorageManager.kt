/**
 * 数据存储管理器
 *
 * 负责管理和存储从UART接收的数据：
 * 1. 数据存储：
 *    - CSV格式文件存储
 *    - 按时间戳命名文件
 *    - 自动分割大文件
 *    - 支持多通道数据
 *
 * 2. 数据管理：
 *    - 缓存最新数据
 *    - 批量写入文件
 *    - 自动清理过期数据
 *    - 内存优化
 *
 * 3. 存储特性：
 *    - 异步写入操作
 *    - 批量写入优化
 *    - 错误处理机制
 *    - 文件大小控制
 *
 * 4. 性能优化：
 *    - 使用缓冲写入
 *    - 合理的刷新策略
 *    - 内存使用优化
 */

package no.nordicsemi.android.uart.data

import android.content.Context
import android.os.Environment
import android.util.Log
import androidx.compose.runtime.*
import java.io.*
import java.text.SimpleDateFormat
import java.util.*
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.Collections

private const val TAG = "DataStorageManager"
private const val BUFFER_SIZE = 2000
private const val MAX_RECORDS_PER_FILE = 120_000
private const val FILE_NAME_PREFIX = "uart_data"

@Composable
internal fun DataStorageManager(
    bodydataPoints: List<Int>,
    ambientdataPoints: List<Int>,
    isRecording: Boolean
) {
    val scope = rememberCoroutineScope()
    val mutex = remember { Mutex() }

    // 使用线程安全的数据缓冲区
    val dataBuffer = remember { Collections.synchronizedList(mutableListOf<Pair<Int?, Int?>>()) }
    var currentFileCount by remember { mutableStateOf(0) }
    var totalRecordCount by remember { mutableStateOf(0) }

    // 创建基础目录
    val baseDir = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS), "uart_data")
    if (!baseDir.exists()) {
        baseDir.mkdirs()
    }

    // 保存数据到文件的函数
    suspend fun saveBufferedData() {
        mutex.withLock {
            if (dataBuffer.isEmpty()) return

            try {
                val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                val fileName = "${FILE_NAME_PREFIX}_${timestamp}_${currentFileCount}.csv"
                val file = File(baseDir, fileName)

                // 创建数据的副本，避免并发修改
                val dataToSave = ArrayList(dataBuffer)
                dataBuffer.clear()

                // 检查文件是否存在，如果不存在则创建并写入表头
                val fileExists = file.exists()
                BufferedWriter(FileWriter(file, fileExists), 8192).use { writer ->
                    if (!fileExists) {
                        writer.write("Time(s),Body Sound,Ambient Sound\n")
                    }

                    val stringBuilder = StringBuilder()
                    var timeValue = 0.0
                    val timeIncrement = 1.0 / 2000.0 // 2000Hz采样率

                    dataToSave.forEach { (body, ambient) ->
                        stringBuilder.append(String.format("%.3f", timeValue))
                            .append(',')
                            .append(body ?: "")
                            .append(',')
                            .append(ambient ?: "")
                            .append('\n')
                        timeValue += timeIncrement
                    }

                    writer.write(stringBuilder.toString())
                    writer.flush()
                }

//                Log.d(TAG, "批量数据已保存到文件: $fileName, 数据量: ${dataToSave.size}")

            } catch (e: Exception) {
//                Log.e(TAG, "保存数据失败: ${e.message}", e)
            }
        }
    }

    // 数据变化时的处理
    LaunchedEffect(bodydataPoints, ambientdataPoints) {
        // 只在录制状态下处理数据
        if (!isRecording) return@LaunchedEffect

        val newData = List(maxOf(
            bodydataPoints.size,
            ambientdataPoints.size
        )) { index ->
            Pair(
                if (index < bodydataPoints.size) bodydataPoints[index] else null,
                if (index < ambientdataPoints.size) ambientdataPoints[index] else null
            )
        }

        mutex.withLock {
            // 添加新数据到缓冲区
            dataBuffer.addAll(newData)
            totalRecordCount += newData.size

            // 当数据量达到阈值时保存
            if (totalRecordCount >= MAX_RECORDS_PER_FILE) { // 120,000条记录
                scope.launch(Dispatchers.IO) {
                    saveBufferedData()
                    currentFileCount++
                    totalRecordCount = 0
                }
            }
        }
    }

    // 停止录制时保存剩余数据
    LaunchedEffect(isRecording) {
        if (!isRecording && dataBuffer.isNotEmpty()) {
            scope.launch(Dispatchers.IO) {
                saveBufferedData()
            }
        }
    }

    // 组件销毁时保存剩余数据
    DisposableEffect(Unit) {
        onDispose {
            scope.launch(Dispatchers.IO) {
                saveBufferedData()
            }
        }
    }
}













