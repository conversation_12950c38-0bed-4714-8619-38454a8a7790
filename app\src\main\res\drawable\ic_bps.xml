<!--
  ~ Copyright (c) 2022, Nordic Semiconductor
  ~ All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without modification, are
  ~ permitted provided that the following conditions are met:
  ~
  ~ 1. Redistributions of source code must retain the above copyright notice, this list of
  ~ conditions and the following disclaimer.
  ~
  ~ 2. Redistributions in binary form must reproduce the above copyright notice, this list
  ~ of conditions and the following disclaimer in the documentation and/or other materials
  ~ provided with the distribution.
  ~
  ~ 3. Neither the name of the copyright holder nor the names of its contributors may be
  ~ used to endorse or promote products derived from this software without specific prior
  ~ written permission.
  ~
  ~ THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  ~ "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
  ~ TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
  ~ PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
  ~ HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  ~ <PERSON>ECIA<PERSON>, E<PERSON>EMPLAR<PERSON>, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
  ~ LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
  ~ OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  ~ OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  ~ NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
  ~ EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="80dp"
    android:height="80dp"
    android:viewportWidth="1024"
    android:viewportHeight="1024">
    <path
        android:fillColor="#00B3DC"
        android:pathData="M773.3,436.5c0,0 -0.1,-0.1 -0.1,-0.1L513,17.6c-5.2,-8.4 -14.4,-13.5 -24.2,-13.5s-19,5.1 -24.2,13.5L204.4,436.4c0,0 -0.1,0.1 -0.1,0.1c-20,32.6 -66.5,117.2 -66.5,198.7c0,46 9.3,90.6 27.7,132.6c17.7,40.5 43.1,76.8 75.4,108c66.3,63.9 154.3,99.1 247.8,99.1c93.6,0 181.6,-35.2 247.8,-99.1c32.3,-31.2 57.7,-67.5 75.4,-108c18.4,-42 27.7,-86.6 27.7,-132.6C839.8,553.7 793.3,469.1 773.3,436.5zM488.8,917.8c-162.1,0 -294,-126.8 -294,-282.6c0,-68.8 44.4,-146.5 58,-168.8l236,-379.8l236,379.8c13.7,22.3 58,100.1 58,168.8C782.8,791 650.9,917.8 488.8,917.8z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M405.2,423.6c-1.5,-2.2 -3.8,-2.2 -5.3,0.1l-53.4,81.7c-2.5,3.8 -0.8,10.5 2.7,10.5h35v246.4c0,10.5 8.5,19 19,19s19,-8.5 19,-19V515.9h35c3.5,0 5.2,-6.8 2.6,-10.6L405.2,423.6z" />
    <path
        android:fillColor="#00B3DC"
        android:pathData="M628.3,696.8h-35V450.4c0,-10.5 -8.5,-19 -19,-19s-19,8.5 -19,19v246.4h-35c-3.5,0 -5.2,6.8 -2.6,10.6l54.7,81.7c1.5,2.2 3.8,2.2 5.3,-0.1l53.4,-81.7C633.5,703.5 631.8,696.8 628.3,696.8z" />
</vector>
