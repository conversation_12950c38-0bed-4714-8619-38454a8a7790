/*
 * KeyValueField.kt
 *
 * 该文件定义了一个键值对显示组件，用于在应用程序中展示标签和对应的值。
 * 主要功能：
 * 1. 提供一个水平布局的键值对显示组件
 * 2. 支持自定义键和值的文本内容
 * 3. 使用Material Design样式
 * 4. 包含预览功能，方便开发时查看组件效果
 */

package no.nordicsemi.android.ui.view

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview

@Composable
fun KeyValueField(key: String, value: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(text = key)
        Text(
            color = MaterialTheme.colorScheme.onBackground,
            text = value
        )
    }
}

/*
预览
 */
@Preview(showBackground = true)
@Composable
fun KeyValueFieldPreview() {
    KeyValueField(key = "Battery Level", value = "75%")
}

