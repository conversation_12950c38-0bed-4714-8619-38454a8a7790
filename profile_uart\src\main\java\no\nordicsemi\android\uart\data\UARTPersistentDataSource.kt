/**
 * UART配置持久化数据源
 *
 * 负责UART配置的持久化存储和读取：
 * 1. 数据转换功能：
 *    - 数据库实体与领域模型之间的转换
 *    - XML序列化与反序列化
 *
 * 2. 配置管理：
 *    - 获取所有配置
 *    - 保存配置
 *    - 删除配置
 *
 * 3. 特点：
 *    - 使用Room数据库存储
 *    - 支持XML格式导入导出
 *    - 使用Flow进行响应式数据流
 *    - 采用单例模式确保全局唯一实例
 */

package no.nordicsemi.android.uart.data

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import nl.adaptivity.xmlutil.serialization.XML
import no.nordicsemi.android.uart.db.Configuration
import no.nordicsemi.android.uart.db.ConfigurationsDao
import no.nordicsemi.android.uart.db.XmlConfiguration
import no.nordicsemi.android.uart.db.XmlMacro
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
internal class UARTPersistentDataSource @Inject constructor(
    private val configurationsDao: ConfigurationsDao,
) {
    private val serializer = XML {
        recommended()
    }

    fun getConfigurations(): Flow<List<UARTConfiguration>> = configurationsDao.load()
        .map { list ->
            list.mapNotNull { it.toDomain() }
        }

    private fun Configuration.toDomain(): UARTConfiguration? {
        return try {
            val xml: String = xml
            val configuration = serializer.decodeFromString<XmlConfiguration>(xml)

            UARTConfiguration(
                _id,
                configuration.name,
                createMacro(configuration.commands.commands)
            )
        } catch (t: Throwable) {
            t.printStackTrace()
            null
        }
    }

    private fun createMacro(macros: Array<XmlMacro>): List<UARTMacro?> {
        return macros.map {
            if (it.command == null) return@map null
            val icon = MacroIcon.create(it.iconIndex)
            UARTMacro(icon, it.command, it.eol)
        }
    }

    suspend fun saveConfiguration(configuration: UARTConfiguration) {
        val xml = serializer.encodeToString(configuration.toXmlConfiguration())

        configurationsDao.insert(Configuration(configuration.id, configuration.name, xml, 0))
    }

    suspend fun deleteConfiguration(configuration: UARTConfiguration) {
        configurationsDao.delete(configuration.name)
    }

    private fun UARTConfiguration.toXmlConfiguration(): XmlConfiguration {
        val xmlConfiguration = XmlConfiguration()
        xmlConfiguration.name = name
        val commands = macros.map { macro ->
            macro?.let {
                XmlMacro().apply {
                    eolIndex = it.newLineChar.index
                    command = it.command
                    iconIndex = it.icon.index
                }
            } ?: XmlMacro()
        }.toTypedArray()
        xmlConfiguration.commands = XmlConfiguration.Commands(commands = commands)
        return xmlConfiguration
    }
}
