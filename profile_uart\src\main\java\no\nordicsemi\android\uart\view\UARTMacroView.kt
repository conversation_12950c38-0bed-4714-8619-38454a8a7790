/**
 * UART宏命令视图
 *
 * 实现了宏命令的详细显示和操作界面：
 * 1. 界面组件：
 *    - 宏命令名称显示
 *    - 命令内容展示
 *    - 执行按钮
 *    - 编辑/删除选项
 *
 * 2. 命令操作：
 *    - 执行宏命令
 *    - 编辑命令内容
 *    - 删除宏命令
 *    - 复制命令文本
 *
 * 3. 显示功能：
 *    - 命令格式化显示
 *    - 行结束符可视化
 *    - 执行状态反馈
 *
 * 4. 交互特性：
 *    - 点击执行命令
 *    - 长按显示选项
 *    - 滑动删除命令
 */

package no.nordicsemi.android.uart.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import no.nordicsemi.android.uart.R
import no.nordicsemi.android.uart.data.UARTConfiguration
import no.nordicsemi.android.uart.data.UARTMacro

private val divider = 4.dp

@Composable
internal fun UARTMacroView(
    configuration: UARTConfiguration,
    isEdited: Boolean,
    onEvent: (UARTViewEvent) -> Unit
) {
    BoxWithConstraints {
        val buttonSize = if (maxWidth < 260.dp) {
            48.dp //Minimum touch area
        }  else {
            80.dp
        }

        Column(modifier = Modifier.padding(horizontal = 16.dp)) {

            Row {
                Item(configuration, isEdited, 0, buttonSize, onEvent)
                Spacer(modifier = Modifier.size(divider))
                Item(configuration, isEdited, 1, buttonSize, onEvent)
                Spacer(modifier = Modifier.size(divider))
                Item(configuration, isEdited, 2, buttonSize, onEvent)
            }

            Spacer(modifier = Modifier.size(divider))

            Row {
                Item(configuration, isEdited, 3, buttonSize, onEvent)
                Spacer(modifier = Modifier.size(divider))
                Item(configuration, isEdited, 4, buttonSize, onEvent)
                Spacer(modifier = Modifier.size(divider))
                Item(configuration, isEdited, 5, buttonSize, onEvent)
            }

            Spacer(modifier = Modifier.size(divider))

            Row {
                Item(configuration, isEdited, 6, buttonSize, onEvent)
                Spacer(modifier = Modifier.size(divider))
                Item(configuration, isEdited, 7, buttonSize, onEvent)
                Spacer(modifier = Modifier.size(divider))
                Item(configuration, isEdited, 8, buttonSize, onEvent)
            }
        }
    }
}

@Composable
private fun Item(
    configuration: UARTConfiguration,
    isEdited: Boolean,
    position: Int,
    buttonSize: Dp,
    onEvent: (UARTViewEvent) -> Unit
) {
    val macro = configuration.macros.getOrNull(position)

    if (macro == null) {
        EmptyButton(isEdited, position, buttonSize, onEvent)
    } else {
        MacroButton(macro, position, isEdited, buttonSize, onEvent)
    }
}

@Composable
private fun MacroButton(
    macro: UARTMacro,
    position: Int,
    isEdited: Boolean,
    buttonSize: Dp,
    onEvent: (UARTViewEvent) -> Unit
) {
    Image(
        painter = painterResource(id = macro.icon.toResId()),
        contentDescription = stringResource(id = R.string.uart_macro_icon),
        colorFilter = ColorFilter.tint(MaterialTheme.colorScheme.onPrimary),
        modifier = Modifier
            .size(buttonSize)
            .clip(RoundedCornerShape(10.dp))
            .clickable {
                if (isEdited) {
                    onEvent(OnEditMacro(position))
                } else {
                    onEvent(OnRunMacro(macro))
                }
            }
            .background(getBackground(isEdited))
    )
}

@Composable
private fun EmptyButton(
    isEdited: Boolean,
    position: Int,
    buttonSize: Dp,
    onEvent: (UARTViewEvent) -> Unit
) {
    Box(
        modifier = Modifier
            .size(buttonSize)
            .clip(RoundedCornerShape(10.dp))
            .clickable {
                if (isEdited) {
                    onEvent(OnEditMacro(position))
                }
            }
            .background(getBackground(isEdited))
    )
}

@Composable
private fun getBackground(isEdited: Boolean): Color {
    return if (!isEdited) {
        MaterialTheme.colorScheme.primary
    } else {
        MaterialTheme.colorScheme.tertiary
    }
}
