<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2022, Nordic Semiconductor
  ~ All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without modification, are
  ~ permitted provided that the following conditions are met:
  ~
  ~ 1. Redistributions of source code must retain the above copyright notice, this list of
  ~ conditions and the following disclaimer.
  ~
  ~ 2. Redistributions in binary form must reproduce the above copyright notice, this list
  ~ of conditions and the following disclaimer in the documentation and/or other materials
  ~ provided with the distribution.
  ~
  ~ 3. Neither the name of the copyright holder nor the names of its contributors may be
  ~ used to endorse or promote products derived from this software without specific prior
  ~ written permission.
  ~
  ~ THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
  ~ "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
  ~ TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
  ~ PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
  ~ HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
  ~ SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
  ~ LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
  ~ OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  ~ OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  ~ NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
  ~ EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  -->
<level-list  xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:maxLevel="0" android:drawable="@drawable/ic_uart_left" />
    <item android:maxLevel="1" android:drawable="@drawable/ic_uart_up" />
    <item android:maxLevel="2" android:drawable="@drawable/ic_uart_right" />
    <item android:maxLevel="3" android:drawable="@drawable/ic_uart_down" />
    <item android:maxLevel="4" android:drawable="@drawable/ic_uart_settings" />
    <item android:maxLevel="5" android:drawable="@drawable/ic_uart_rewind" />
    <item android:maxLevel="6" android:drawable="@drawable/ic_uart_play" />
    <item android:maxLevel="7" android:drawable="@drawable/ic_uart_pause" />
    <item android:maxLevel="8" android:drawable="@drawable/ic_uart_stop" />
    <item android:maxLevel="9" android:drawable="@drawable/ic_uart_forward" />
    <item android:maxLevel="10" android:drawable="@drawable/ic_uart_about" />
    <item android:maxLevel="11" android:drawable="@drawable/ic_uart_1" />
    <item android:maxLevel="12" android:drawable="@drawable/ic_uart_2" />
    <item android:maxLevel="13" android:drawable="@drawable/ic_uart_3" />
    <item android:maxLevel="14" android:drawable="@drawable/ic_uart_4" />
    <item android:maxLevel="15" android:drawable="@drawable/ic_uart_5" />
    <item android:maxLevel="16" android:drawable="@drawable/ic_uart_6" />
    <item android:maxLevel="17" android:drawable="@drawable/ic_uart_7" />
    <item android:maxLevel="18" android:drawable="@drawable/ic_uart_8" />
    <item android:maxLevel="19" android:drawable="@drawable/ic_uart_9" />
</level-list>
