/*
 * TopAppBar.kt
 *
 * 该文件定义了应用程序的顶部应用栏组件，提供了多种类型的应用栏实现。
 * 主要功能：
 * 1. CloseIconAppBar: 带关闭图标的应用栏
 * 2. LoggerIconAppBar: 带日志记录器图标的应用栏
 * 3. ProfileAppBar: 用于设备配置文件的应用栏
 * 4. 支持设备连接状态显示
 * 5. 提供断开连接和日志查看功能
 * 6. 包含预览功能，方便开发时查看各种应用栏效果
 */

package no.nordicsemi.android.ui.view

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import no.nordicsemi.android.common.logger.view.LoggerAppBarIcon
import no.nordicsemi.android.common.ui.view.NordicAppBar
import no.nordicsemi.android.kotlin.ble.core.data.GattConnectionState
import no.nordicsemi.android.kotlin.ble.core.data.GattConnectionStateWithStatus
import no.nordicsemi.android.ui.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CloseIconAppBar(text: String, onClick: () -> Unit) {
    NordicAppBar(
        title = { Text(text) },
        backButtonIcon = Icons.Default.Close,
        onNavigationButtonClick = onClick,
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoggerIconAppBar(
    text: String,
    onClick: () -> Unit,
    isConnected: Boolean,
    onDisconnectClick: () -> Unit,
    onLoggerClick: () -> Unit
) {
    NordicAppBar(
        title = { Text(text) },
        onNavigationButtonClick = onClick,
        actions = {
            TextButton(
                onClick = onDisconnectClick,
                enabled = isConnected,
            ) {
                Text(stringResource(id = R.string.disconnect))
            }
            LoggerAppBarIcon(
                onClick = onLoggerClick
            )
        }
    )
}

@Composable
fun ProfileAppBar(
    deviceName: String?,
    connectionState: GattConnectionStateWithStatus?,
    navigateUp: () -> Unit,
    disconnect: () -> Unit,
    openLogger: () -> Unit
) {
    val isConnected = connectionState?.state == GattConnectionState.STATE_CONNECTED
    LoggerIconAppBar(deviceName ?: "No name", navigateUp, isConnected, disconnect, openLogger)
}


@Preview(showBackground = true)
@Composable
fun CloseIconAppBarPreview() {
    CloseIconAppBar(
        text = "Close AppBar",
        onClick = { /* Do nothing */ }
    )
}


@Preview(showBackground = true)
@Composable
fun LoggerIconAppBarPreview() {
    LoggerIconAppBar(
        text = "Logger AppBar",
        onClick = { /* Do nothing */ },
        isConnected = true,  // 模拟设备连接状态
        onDisconnectClick = { /* Do nothing */ },
        onLoggerClick = { /* Do nothing */ }
    )
}

@Preview(showBackground = true)
@Composable
fun ProfileAppBarPreview() {
    ProfileAppBar(
        deviceName = "My Device",
        connectionState = null,
        navigateUp = { /* Do nothing */ },
        disconnect = { /* Do nothing */ },
        openLogger = { /* Do nothing */ }
    )
}
