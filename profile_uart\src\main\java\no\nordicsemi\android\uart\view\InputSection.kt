/**
 * UART命令输入组件
 *
 * 实现了用户输入命令的界面：
 * 1. 输入区域：
 *    - 多行文本输入框
 *    - 自动滚动支持
 *    - 最大高度限制
 *    - 占位符提示
 *
 * 2. 发送控制：
 *    - 发送按钮
 *    - 行结束符选择
 *    - 输入清空功能
 *
 * 3. 布局设计：
 *    - 水平布局排列
 *    - 响应式宽度
 *    - 合理的间距
 *
 * 4. 状态管理：
 *    - 输入文本状态
 *    - 行结束符状态
 *    - 状态持久化
 */

package no.nordicsemi.android.uart.view

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import no.nordicsemi.android.common.ui.view.RadioButtonGroup
import no.nordicsemi.android.common.ui.view.RadioButtonItem
import no.nordicsemi.android.common.ui.view.RadioGroupViewEntity
import no.nordicsemi.android.uart.R
import no.nordicsemi.android.uart.data.MacroEol
import no.nordicsemi.android.ui.view.ScreenSection
import no.nordicsemi.android.ui.view.SectionTitle
import no.nordicsemi.android.utils.EMPTY

@Composable
internal fun InputSection(onEvent: (UARTViewEvent) -> Unit) {
    val text = rememberSaveable { mutableStateOf(String.EMPTY) }
    val hint = stringResource(id = R.string.uart_input_hint)
    val checkedItem = rememberSaveable { mutableStateOf(MacroEol.entries[0]) }

    Row(verticalAlignment = Alignment.CenterVertically) {
        Box(modifier = Modifier.weight(1f)) {

            val scope = rememberCoroutineScope()
            val scrollState = rememberScrollState()

            OutlinedTextField(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 65.dp)
                    .verticalScroll(scrollState),
                value = text.value,
                label = { Text(hint) },
                onValueChange = { newValue: String ->
                    text.value = newValue
                    scope.launch {
                        scrollState.scrollTo(Int.MAX_VALUE)
                    }
                }
            )
        }

        Spacer(modifier = Modifier.size(16.dp))

        Button(
            onClick = {
                onEvent(OnRunInput(text.value, checkedItem.value))
                text.value = String.EMPTY
            },
            modifier = Modifier.padding(top = 6.dp)
        ) {
            Text(text = stringResource(id = R.string.uart_send))
        }
    }
}

@Composable
internal fun EditInputSection(onEvent: (UARTViewEvent) -> Unit) {
    val checkedItem = rememberSaveable { mutableStateOf(MacroEol.entries[0]) }

    val items = MacroEol.entries.map {
        RadioButtonItem(it.toDisplayString(), it == checkedItem.value)
    }
    val viewEntity = RadioGroupViewEntity(items)

    ScreenSection {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            SectionTitle(
                resId = R.drawable.ic_input,
                title = stringResource(R.string.uart_input),
                menu = {
                    IconButton(onClick = { onEvent(MacroInputSwitchClick) }) {
                        Icon(
                            painterResource(id = R.drawable.ic_macro),
                            contentDescription = stringResource(id = R.string.uart_input_macro),
                        )
                    }
                }
            )

            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = stringResource(id = R.string.uart_macro_dialog_eol),
                    style = MaterialTheme.typography.labelLarge
                )

                RadioButtonGroup(viewEntity) {
                    val i = items.indexOf(it)
                    checkedItem.value = MacroEol.entries.toTypedArray()[i]
                }
            }

            Spacer(modifier = Modifier.size(16.dp))
        }
    }
}
