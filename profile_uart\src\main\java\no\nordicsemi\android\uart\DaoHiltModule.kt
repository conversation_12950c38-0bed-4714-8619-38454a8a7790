/**
 * UART DAO依赖注入模块
 *
 * 使用Hilt实现DAO层的依赖注入配置：
 * 1. DAO注入：
 *    - 配置DAO
 *    - 宏命令DAO
 *    - 通信记录DAO
 *
 * 2. 数据库访问：
 *    - 提供数据库实例
 *    - 配置数据库访问
 *    - 管理DAO生命周期
 *
 * 3. 依赖管理：
 *    - 单例模式
 *    - 作用域控制
 *    - 依赖关系配置
 *
 * 4. 模块配置：
 *    - 模块声明
 *    - 绑定规则
 *    - 提供者方法
 */

package no.nordicsemi.android.uart

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import no.nordicsemi.android.uart.db.ConfigurationsDao
import no.nordicsemi.android.uart.db.ConfigurationsDatabase
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class DaoHiltModule {

    @Provides
    @Singleton
    internal fun provideDao(db: ConfigurationsDatabase): ConfigurationsDao {
        return db.dao()
    }
}
