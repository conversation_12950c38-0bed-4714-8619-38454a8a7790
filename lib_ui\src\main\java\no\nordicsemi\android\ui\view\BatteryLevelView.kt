/*
 * BatteryLevelView.kt
 *
 * 该文件定义了一个电池电量显示组件，用于在应用程序中展示设备的电池状态。
 * 主要功能：
 * 1. 显示设备电池电量百分比
 * 2. 使用KeyValueField组件展示电量信息
 * 3. 提供预览功能，方便开发时查看组件效果
 */

package no.nordicsemi.android.ui.view

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import no.nordicsemi.android.ui.R

@Composable
fun BatteryLevelView(batteryLevel: Int) {
    ScreenSection {
        KeyValueField(
            stringResource(id = R.string.field_battery),
            "$batteryLevel%"
        )
    }
}

/*
预览
 */
@Preview(showBackground = true)
@Composable
fun BatteryLevelViewPreview() {
    BatteryLevelView(batteryLevel = 50)  // 示例的电池电量
}
