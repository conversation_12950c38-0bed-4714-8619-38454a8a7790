/**
 * UART数据库依赖注入模块
 *
 * 使用Hilt实现数据库层的依赖注入配置：
 * 1. 数据库配置：
 *    - 数据库实例创建
 *    - 数据库版本管理
 *    - 迁移策略
 *
 * 2. 依赖提供：
 *    - Room数据库
 *    - 数据库Builder
 *    - 数据库回调
 *
 * 3. 作用域管理：
 *    - 单例配置
 *    - 组件绑定
 *    - 生命周期控制
 *
 * 4. 模块设置：
 *    - 安装规则
 *    - 提供方法
 *    - 依赖声明
 */

package no.nordicsemi.android.uart

import android.content.Context
import androidx.room.Room
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import no.nordicsemi.android.uart.db.ConfigurationsDatabase
import no.nordicsemi.android.uart.db.MIGRATION_1_2
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class DbHiltModule {

    @Provides
    @Singleton
    internal fun provideDB(@ApplicationContext context: Context): ConfigurationsDatabase {
        return Room.databaseBuilder(
            context,
            ConfigurationsDatabase::class.java, "toolbox_uart.db"
        ).addMigrations(MIGRATION_1_2).build()
    }
}
