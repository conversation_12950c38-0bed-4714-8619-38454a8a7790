/**
 * 数据库迁移定义类
 *
 * 定义了数据库版本迁移策略：
 * 1. 版本迁移：
 *    - 从版本1升级到版本2
 *    - 当前为空实现，因为架构没有变化
 *
 * 2. 实现方式：
 *    - 继承Migration基类
 *    - 重写migrate方法
 *    - 使用SupportSQLiteDatabase执行迁移
 *
 * 3. 用途：
 *    - 确保数据库版本升级时的数据安全
 *    - 保持向后兼容性
 */

package no.nordicsemi.android.uart.db

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

val MIGRATION_1_2 = object : Migration(1, 2) {
    override fun migrate(db: SupportSQLiteDatabase) {
        // Empty implementation, because the schema isn't changing.
    }
}
