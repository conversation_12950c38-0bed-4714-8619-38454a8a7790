/**
 * UART配置数据库定义类
 *
 * Room数据库的主要配置类：
 * 1. 数据库设置：
 *    - 实体类：Configuration
 *    - 数据库版本：2
 *
 * 2. 功能：
 *    - 提供数据库访问对象（DAO）
 *    - 管理数据库版本
 *    - 继承RoomDatabase基类
 *
 * 3. 特点：
 *    - 使用@Database注解定义数据库
 *    - 采用抽象类实现
 *    - 单表数据库设计
 */

package no.nordicsemi.android.uart.db

import androidx.room.Database
import androidx.room.RoomDatabase

@Database(entities = [Configuration::class], version = 2)
internal abstract class ConfigurationsDatabase : RoomDatabase() {
    abstract fun dao(): ConfigurationsDao
}
